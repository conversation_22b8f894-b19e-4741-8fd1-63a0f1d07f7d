class Store {
  final String id;
  final String name;
  final String address;
  final String city;
  final String postalCode;
  final String contactPerson;
  final String contactPhone;
  final String contactEmail;
  final List<String> deviceIds; // IDs för IoT-enheter i butiken
  
  Store({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.postalCode,
    this.contactPerson = '',
    this.contactPhone = '',
    this.contactEmail = '',
    this.deviceIds = const [],
  });
  
  String get fullAddress => '$address, $postalCode $city';
  
  Store copyWith({
    String? id,
    String? name,
    String? address,
    String? city,
    String? postalCode,
    String? contactPerson,
    String? contactPhone,
    String? contactEmail,
    List<String>? deviceIds,
  }) {
    return Store(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      city: city ?? this.city,
      postalCode: postalCode ?? this.postalCode,
      contactPerson: contactPerson ?? this.contactPerson,
      contactPhone: contactPhone ?? this.contactPhone,
      contactEmail: contactEmail ?? this.contactEmail,
      deviceIds: deviceIds ?? this.deviceIds,
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'city': city,
      'postalCode': postalCode,
      'contactPerson': contactPerson,
      'contactPhone': contactPhone,
      'contactEmail': contactEmail,
      'deviceIds': deviceIds,
    };
  }
  
  factory Store.fromMap(Map<String, dynamic> map) {
    return Store(
      id: map['id'],
      name: map['name'],
      address: map['address'],
      city: map['city'],
      postalCode: map['postalCode'],
      contactPerson: map['contactPerson'] ?? '',
      contactPhone: map['contactPhone'] ?? '',
      contactEmail: map['contactEmail'] ?? '',
      deviceIds: List<String>.from(map['deviceIds'] ?? []),
    );
  }
}
