import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/guard_order.dart';
import '../../services/store_service.dart';
import '../../services/auth_service.dart';
import '../../services/file_service.dart';
import '../../utils/responsive_layout.dart';
import '../../widgets/attachment_list.dart';

class GuardOrderAdminDetailScreen extends StatefulWidget {
  final String orderId;

  const GuardOrderAdminDetailScreen({
    super.key,
    required this.orderId,
  });

  @override
  State<GuardOrderAdminDetailScreen> createState() => _GuardOrderAdminDetailScreenState();
}

class _GuardOrderAdminDetailScreenState extends State<GuardOrderAdminDetailScreen> {
  final _storeService = StoreService();
  final _authService = AuthService();
  final _feedbackController = TextEditingController();

  late GuardOrder? _order;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadOrder();
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  void _loadOrder() {
    setState(() {
      _isLoading = true;
    });

    try {
      // Skriv ut debug-information om admin-status
      debugPrint('Admin-status: ${_authService.isAdmin}');

      final order = _storeService.getGuardOrderById(widget.orderId);

      // Skriv ut debug-information om beställningen
      if (order != null) {
        debugPrint('Beställning hittad: ${order.id}');
        debugPrint('Status: ${order.status}');
        debugPrint('Feedback: ${order.adminFeedback}');
      } else {
        debugPrint('Beställning hittades inte med ID: ${widget.orderId}');
      }

      setState(() {
        _order = order;
        _isLoading = false;

        // Fyll i feedback om det finns
        if (order != null && order.adminFeedback != null) {
          _feedbackController.text = order.adminFeedback!;
        }
      });
    } catch (e) {
      debugPrint('Fel vid laddning av beställning: $e');
      setState(() {
        _order = null;
        _isLoading = false;
      });
    }
  }

  Future<void> _updateOrderStatus(OrderStatus status) async {
    if (_order == null) return;

    // Kontrollera admin-status
    if (!_authService.isAdmin) {
      debugPrint('Användaren är inte admin! Kan inte uppdatera beställning.');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Du har inte behörighet att uppdatera beställningar'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Visa bekräftelsedialog
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getStatusActionTitle(status)),
        content: Text(_getStatusActionMessage(status)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Avbryt'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getStatusColor(status),
              foregroundColor: Colors.white,
            ),
            child: Text(_getStatusActionButton(status)),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() {
      _isSaving = true;
    });

    // Skriv ut debug-information innan uppdatering
    debugPrint('Förbereder uppdatering av beställning ${_order!.id}');
    debugPrint('Nuvarande status: ${_order!.status}');
    debugPrint('Ny status: $status');
    debugPrint('Feedback: ${_feedbackController.text.trim()}');

    try {
      // Uppdatera beställningen direkt med en ny kopia
      final oldOrder = _order!;
      final updatedOrder = oldOrder.copyWith(
        status: status,
        updatedAt: DateTime.now(),
        adminFeedback: _feedbackController.text.trim(),
      );

      // Skriv ut debug-information om den uppdaterade beställningen
      debugPrint('Direkt uppdatering av beställning:');
      debugPrint('Gammal status: ${oldOrder.status}');
      debugPrint('Ny status: ${updatedOrder.status}');
      debugPrint('Gammal feedback: ${oldOrder.adminFeedback}');
      debugPrint('Ny feedback: ${updatedOrder.adminFeedback}');

      // Uppdatera beställningen i StoreService
      final index = _storeService.getAllGuardOrders().indexWhere((o) => o.id == oldOrder.id);
      if (index != -1) {
        // Uppdatera beställningen direkt i listan
        _storeService.updateOrderDirectly(updatedOrder);

        setState(() {
          _order = updatedOrder;
          _isSaving = false;
        });

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Beställningen har ${_getStatusActionPast(status)}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        setState(() {
          _isSaving = false;
        });

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Kunde inte hitta beställningen'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('Fel vid uppdatering av beställning: $e');

      setState(() {
        _isSaving = false;
      });

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Ett fel uppstod: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _getStatusActionTitle(OrderStatus status) {
    switch (status) {
      case OrderStatus.approved:
        return 'Godkänn beställning';
      case OrderStatus.rejected:
        return 'Avvisa beställning';
      case OrderStatus.completed:
        return 'Markera som genomförd';
      case OrderStatus.cancelled:
        return 'Avbryt beställning';
      default:
        return 'Uppdatera status';
    }
  }

  String _getStatusActionMessage(OrderStatus status) {
    switch (status) {
      case OrderStatus.approved:
        return 'Är du säker på att du vill godkänna denna beställning?';
      case OrderStatus.rejected:
        return 'Är du säker på att du vill avvisa denna beställning?';
      case OrderStatus.completed:
        return 'Är du säker på att du vill markera denna beställning som genomförd?';
      case OrderStatus.cancelled:
        return 'Är du säker på att du vill avbryta denna beställning?';
      default:
        return 'Är du säker på att du vill uppdatera statusen för denna beställning?';
    }
  }

  String _getStatusActionButton(OrderStatus status) {
    switch (status) {
      case OrderStatus.approved:
        return 'Godkänn';
      case OrderStatus.rejected:
        return 'Avvisa';
      case OrderStatus.completed:
        return 'Markera som genomförd';
      case OrderStatus.cancelled:
        return 'Avbryt';
      default:
        return 'Uppdatera';
    }
  }

  String _getStatusActionPast(OrderStatus status) {
    switch (status) {
      case OrderStatus.approved:
        return 'godkänts';
      case OrderStatus.rejected:
        return 'avvisats';
      case OrderStatus.completed:
        return 'markerats som genomförd';
      case OrderStatus.cancelled:
        return 'avbrutits';
      default:
        return 'uppdaterats';
    }
  }

  String _getStatusActionInfinitive(OrderStatus status) {
    switch (status) {
      case OrderStatus.approved:
        return 'godkänna';
      case OrderStatus.rejected:
        return 'avvisa';
      case OrderStatus.completed:
        return 'markera som genomförd';
      case OrderStatus.cancelled:
        return 'avbryta';
      default:
        return 'uppdatera';
    }
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.approved:
        return Colors.green;
      case OrderStatus.rejected:
        return Colors.red;
      case OrderStatus.completed:
        return Colors.blue;
      case OrderStatus.cancelled:
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Beställningsdetaljer'),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_order == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Beställningsdetaljer'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Beställningen hittades inte',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Beställningen du söker efter finns inte eller har tagits bort.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back),
                label: const Text('Tillbaka'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Beställning från ${_order!.storeName}'),
      ),
      body: _isSaving
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: ResponsiveLayout.getAdaptivePadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Statusbanner
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: _order!.statusColor),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: _order!.statusColor,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Status: ${_order!.statusText}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: _order!.statusColor,
                                ),
                              ),
                              if (_order!.updatedAt != null)
                                Text(
                                  'Senast uppdaterad: ${DateFormat('yyyy-MM-dd HH:mm').format(_order!.updatedAt!)}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Beställningsdetaljer
                  Text(
                    'Beställningsdetaljer',
                    style: TextStyle(
                      fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildDetailRow('Beställnings-ID:', _order!.id),
                          _buildDetailRow('Skapad:', DateFormat('yyyy-MM-dd HH:mm').format(_order!.createdAt)),
                          _buildDetailRow('Butik:', _order!.storeName),
                          _buildDetailRow('Datum:', DateFormat('yyyy-MM-dd').format(_order!.requestedDate)),
                          _buildDetailRow('Tid:', '${_order!.startTime} - ${_order!.endTime}'),
                          _buildDetailRow('Typ av vakt:', _order!.guardTypeText),
                          _buildDetailRow('Antal:', '${_order!.numberOfGuards} ${_order!.numberOfGuards == 1 ? 'person' : 'personer'}'),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Beskrivning
                  Text(
                    'Beskrivning från kund',
                    style: TextStyle(
                      fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(_order!.description),
                    ),
                  ),

                  // Bilagor
                  if (_order!.attachmentPaths.isNotEmpty) ...[
                    const SizedBox(height: 24),

                    Text(
                      'Bilagor från kund',
                      style: TextStyle(
                        fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: AttachmentList(
                          attachmentPaths: _order!.attachmentPaths,
                          readOnly: true,
                        ),
                      ),
                    ),
                  ],

                  // Tillstånd för ordningsvakt
                  if (_order!.guardType == GuardType.securityGuard &&
                      _order!.securityGuardPermitPath != null) ...[
                    const SizedBox(height: 24),

                    Text(
                      'Tillstånd för ordningsvakt',
                      style: TextStyle(
                        fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Bifogat tillstånd från Polismyndigheten:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            AttachmentList(
                              attachmentPaths: [_order!.securityGuardPermitPath!],
                              readOnly: true,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],

                  const SizedBox(height: 24),

                  // Statushistorik
                  if (_order!.statusHistory.isNotEmpty) ...[
                    Text(
                      'Statushistorik',
                      style: TextStyle(
                        fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            for (final change in _order!.statusHistory) ...[
                              Builder(builder: (context) {
                                final dateStr = DateFormat('yyyy-MM-dd HH:mm').format(change.timestamp);
                                Color statusColor;
                                switch (change.toStatus) {
                                  case OrderStatus.pending:
                                    statusColor = Colors.orange;
                                    break;
                                  case OrderStatus.approved:
                                    statusColor = Colors.green;
                                    break;
                                  case OrderStatus.rejected:
                                    statusColor = Colors.red;
                                    break;
                                  case OrderStatus.completed:
                                    statusColor = Colors.blue;
                                    break;
                                  case OrderStatus.cancelled:
                                    statusColor = Colors.red;
                                    break;
                                }

                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(Icons.circle, size: 12, color: statusColor),
                                        const SizedBox(width: 8),
                                        Text(
                                          dateStr,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14,
                                          ),
                                        ),
                                        if (change.userName != null) ...[
                                          const SizedBox(width: 8),
                                          Text(
                                            'av ${change.userName}',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey.shade700,
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 20.0),
                                      child: Text(
                                        change.note ?? 'Status ändrad från ${_getStatusText(change.fromStatus)} till ${_getStatusText(change.toStatus)}',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey.shade800,
                                        ),
                                      ),
                                    ),
                                    const Divider(),
                                  ],
                                );
                              }),
                            ],
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],

                  // Feedback från admin
                  Text(
                    'Din feedback till kund',
                    style: TextStyle(
                      fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextField(
                            controller: _feedbackController,
                            decoration: const InputDecoration(
                              labelText: 'Feedback till kund',
                              hintText: 'Skriv din feedback här...',
                              border: OutlineInputBorder(),
                            ),
                            maxLines: 4,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Denna feedback kommer att visas för kunden.',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Åtgärdsknappar
                  if (_order!.status == OrderStatus.pending) ...[
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _updateOrderStatus(OrderStatus.rejected),
                            icon: const Icon(Icons.cancel),
                            label: const Text('AVVISA'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _updateOrderStatus(OrderStatus.approved),
                            icon: const Icon(Icons.check_circle),
                            label: const Text('GODKÄNN'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ] else if (_order!.status == OrderStatus.approved) ...[
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _updateOrderStatus(OrderStatus.cancelled),
                            icon: const Icon(Icons.cancel),
                            label: const Text('AVBRYT'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _updateOrderStatus(OrderStatus.completed),
                            icon: const Icon(Icons.check_circle),
                            label: const Text('GENOMFÖRD'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Väntande';
      case OrderStatus.approved:
        return 'Godkänd';
      case OrderStatus.rejected:
        return 'Avvisad';
      case OrderStatus.completed:
        return 'Genomförd';
      case OrderStatus.cancelled:
        return 'Avbruten';
    }
  }
}
