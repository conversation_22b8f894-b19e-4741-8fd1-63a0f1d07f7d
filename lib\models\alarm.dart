enum AlarmType { yellow, red }

enum AlarmStatus { new_, ongoing, resolved }

class AlarmStatusChange {
  final DateTime timestamp;
  final AlarmStatus fromStatus;
  final AlarmStatus toStatus;
  final String? userId;
  final String? userName;
  final String? note;

  AlarmStatusChange({
    required this.timestamp,
    required this.fromStatus,
    required this.toStatus,
    this.userId,
    this.userName,
    this.note,
  });

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.millisecondsSinceEpoch,
      'fromStatus': fromStatus.index,
      'toStatus': toStatus.index,
      'userId': userId,
      'userName': userName,
      'note': note,
    };
  }

  factory AlarmStatusChange.fromMap(Map<String, dynamic> map) {
    return AlarmStatusChange(
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      fromStatus: AlarmStatus.values[map['fromStatus']],
      toStatus: AlarmStatus.values[map['toStatus']],
      userId: map['userId'],
      userName: map['userName'],
      note: map['note'],
    );
  }
}

class Alarm {
  final String id;
  final DateTime timestamp;
  final String deviceId;
  final String location;
  final AlarmType type;
  final AlarmStatus status;
  final String? assignedUserId;
  final String? assignedUserName;
  final List<AlarmStatusChange> statusHistory;

  Alarm({
    required this.id,
    required this.timestamp,
    required this.deviceId,
    required this.location,
    this.type = AlarmType.red,
    this.status = AlarmStatus.new_,
    this.assignedUserId,
    this.assignedUserName,
    this.statusHistory = const [],
  });

  bool get isHandled => status == AlarmStatus.resolved;
  bool get isAssigned => assignedUserId != null;

  Alarm copyWith({
    String? id,
    DateTime? timestamp,
    String? deviceId,
    String? location,
    AlarmType? type,
    AlarmStatus? status,
    String? assignedUserId,
    String? assignedUserName,
    List<AlarmStatusChange>? statusHistory,
  }) {
    return Alarm(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      deviceId: deviceId ?? this.deviceId,
      location: location ?? this.location,
      type: type ?? this.type,
      status: status ?? this.status,
      assignedUserId: assignedUserId ?? this.assignedUserId,
      assignedUserName: assignedUserName ?? this.assignedUserName,
      statusHistory: statusHistory ?? this.statusHistory,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'deviceId': deviceId,
      'location': location,
      'type': type.index,
      'status': status.index,
      'assignedUserId': assignedUserId,
      'assignedUserName': assignedUserName,
      'statusHistory': statusHistory.map((change) => change.toMap()).toList(),
    };
  }

  factory Alarm.fromMap(Map<String, dynamic> map) {
    return Alarm(
      id: map['id'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      deviceId: map['deviceId'],
      location: map['location'],
      type: AlarmType.values[map['type'] ?? 1], // Default to red if not specified
      status: AlarmStatus.values[map['status'] ?? 0], // Default to new if not specified
      assignedUserId: map['assignedUserId'],
      assignedUserName: map['assignedUserName'],
      statusHistory: map['statusHistory'] != null
          ? List<Map<String, dynamic>>.from(map['statusHistory'])
              .map((item) => AlarmStatusChange.fromMap(item))
              .toList()
          : [],
    );
  }
}
