import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import '../models/alarm.dart';
import '../models/alert.dart';
import '../services/alarm_service.dart';
import '../services/auth_service.dart';
import '../services/alert_service.dart';
import '../widgets/home_button.dart';
import 'alarm_detail_screen.dart';
import 'alert_detail_screen.dart';

class AlarmScreen extends StatefulWidget {
  const AlarmScreen({super.key});

  @override
  State<AlarmScreen> createState() => _AlarmScreenState();
}

class _AlarmScreenState extends State<AlarmScreen> {
  final AlarmService _alarmService = AlarmService();
  final AuthService _authService = AuthService();
  final AlertService _alertService = AlertService();

  List<Alert> _alerts = [];
  bool _isAdmin = false;
  StreamSubscription? _alertsSubscription;
  StreamSubscription? _alarmSubscription;

  @override
  void initState() {
    super.initState();

    // Check if user is admin
    _isAdmin = _authService.isAdmin;
    if (kDebugMode) {
      print('AlarmScreen initialized, isAdmin: $_isAdmin');
    }

    // Listen for new alarms - only update the UI
    // (notifications are now handled by the global listener in NotificationService)
    _alarmSubscription = _alarmService.alarmStream.listen((alarm) {
      if (mounted) {
        setState(() {});
      }
    });

    // If admin, listen for alerts
    if (_isAdmin) {
      if (kDebugMode) {
        print('Setting up alerts listener for admin user');
      }
      _setupAlertsListener();
    }
  }

  @override
  void dispose() {
    _alertsSubscription?.cancel();
    _alarmSubscription?.cancel();
    super.dispose();
  }

  void _setupAlertsListener() {
    // Get all alerts, not just active ones
    _alertsSubscription = _alertService.getAllAlertsStream().listen((alerts) {
      if (mounted) {
        if (kDebugMode) {
          print('AlarmScreen received ${alerts.length} alerts from stream');
        }
        setState(() {
          // Clear the alerts list and add the new alerts
          // This ensures deleted alerts are removed from the UI
          _alerts = [];
          _alerts.addAll(alerts);
        });
      }
    }, onError: (error) {
      if (kDebugMode) {
        print('Error in alerts stream: $error');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading alerts: $error')),
        );
      }
    });
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _viewAlarmDetails(String alarmId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AlarmDetailScreen(alarmId: alarmId),
      ),
    ).then((_) {
      // Uppdatera listan när vi kommer tillbaka från detaljvyn
      setState(() {});
    });
  }

  void _viewAlertDetails(String alertId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AlertDetailScreen(alertId: alertId),
      ),
    ).then((_) {
      // Uppdatera listan när vi kommer tillbaka från detaljvyn
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final alarms = _alarmService.alarms;

    // Create a list to hold all alerts (both Alarm and Alert types)
    final List<dynamic> filteredAlerts = [];

    // First add all alarms from the AlarmService
    // These are already filtered to remove deleted alarms
    filteredAlerts.addAll(alarms);

    // For admin users, also add alerts from the AlertService
    if (_isAdmin) {
      // For debugging
      if (kDebugMode) {
        print('Adding ${_alerts.length} alerts from database to UI');
      }

      // Only add alerts that aren't duplicates of existing alarms
      for (final alert in _alerts) {
        bool isDuplicate = false;

        // Check for duplicates in the alarms list
        for (final alarm in alarms) {
          // If we find a matching alarm with the same ID or matching button ID and similar timestamp
          if (alarm.id == alert.id ||
              (alarm.deviceId == alert.buttonId &&
               alarm.timestamp.difference(alert.pressedAt).inMinutes.abs() < 1)) {
            isDuplicate = true;
            break;
          }
        }

        // Only add if not a duplicate
        if (!isDuplicate) {
          filteredAlerts.add(alert);
        }
      }
    }

    // Sort by timestamp (newest first)
    filteredAlerts.sort((a, b) {
      final DateTime timeA = a is Alarm ? a.timestamp : (a as Alert).pressedAt;
      final DateTime timeB = b is Alarm ? b.timestamp : (b as Alert).pressedAt;
      return timeB.compareTo(timeA);
    });

    // Print final filtered alerts count for debugging
    if (kDebugMode) {
      print('Final filtered alerts count: ${filteredAlerts.length}');
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Larm'),
        actions: const [
          HomeButton(),
        ],
      ),
      body: Column(
        children: [
          // Informationstext
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: Colors.blue,
                      size: 36,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Larmöversikt',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isAdmin
                        ? 'Här visas alla larm från anslutna enheter. Röda larm är högprioriterade och gula larm är lågprioriterade.'
                        : 'Här visas alla larm från anslutna enheter. Larm kan markeras som pågående eller åtgärdade.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const Divider(),
          Expanded(
            child: filteredAlerts.isEmpty
                ? const Center(child: Text('Inga larm har registrerats'))
                : ListView.builder(
                    itemCount: filteredAlerts.length,
                    itemBuilder: (context, index) {
                      final alert = filteredAlerts[index];

                      // Check if this is an Alarm or Alert
                      if (alert is Alarm) {
                        // Handle Alarm type
                        return _buildAlarmCard(alert);
                      } else if (alert is Alert) {
                        // Handle Alert type
                        return _buildAlertCard(alert);
                      }

                      // Fallback (should never happen)
                      return const SizedBox.shrink();
                    },
                  ),
          ),
        ],
      ),
    );
  }

  // Build a card for an Alarm
  Widget _buildAlarmCard(Alarm alarm) {
    // Bestäm färger baserat på larmtyp och status
    Color cardColor;
    Color iconColor;
    Color textColor;
    Color statusColor;
    String statusText;

    switch (alarm.status) {
      case AlarmStatus.new_:
        statusText = 'NYTT';
        statusColor = alarm.type == AlarmType.red ? Colors.red : Colors.orange;
        cardColor = alarm.type == AlarmType.red ? Colors.red.shade50 : Colors.orange.shade50;
        iconColor = alarm.type == AlarmType.red ? Colors.red : Colors.orange;
        textColor = alarm.type == AlarmType.red ? Colors.red.shade700 : Colors.orange.shade700;
        break;
      case AlarmStatus.ongoing:
        statusText = 'PÅGÅENDE';
        statusColor = Colors.blue;
        cardColor = Colors.blue.shade50;
        iconColor = Colors.blue;
        textColor = Colors.blue.shade700;
        break;
      case AlarmStatus.resolved:
        statusText = 'ÅTGÄRDAT';
        statusColor = Colors.green;
        cardColor = Colors.grey.shade100;
        iconColor = Colors.grey;
        textColor = Colors.grey.shade700;
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: cardColor,
      elevation: alarm.status == AlarmStatus.new_ ? 4 : 1,
      child: ListTile(
        leading: Stack(
          alignment: Alignment.center,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: iconColor,
              size: 36,
            ),
            if (alarm.type == AlarmType.red && alarm.status == AlarmStatus.new_)
              Positioned.fill(
                child: TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.5, end: 1.0),
                  duration: const Duration(milliseconds: 800),
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: Icon(
                        Icons.warning_amber_rounded,
                        color: Colors.red.withAlpha(128), // 0.5 * 255 = 128
                        size: 36 * value,
                      ),
                    );
                  },
                  onEnd: () => setState(() {}),
                ),
              ),
          ],
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                '${alarm.type == AlarmType.red ? 'AKUT' : 'VARNING'}: ${alarm.deviceId}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: textColor,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                statusText,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Plats: ${alarm.location}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text('Tid: ${_formatDateTime(alarm.timestamp)}'),
          ],
        ),
        isThreeLine: true,
        onTap: () => _viewAlarmDetails(alarm.id),
      ),
    );
  }

  // Build a card for an Alert
  Widget _buildAlertCard(Alert alert) {
    // Determine colors based on alert type
    Color cardColor;
    Color iconColor;
    Color textColor;
    String priorityText;
    String statusText;
    Color statusColor;

    // Set status based on alert status
    switch (alert.status) {
      case AlertStatus.active:
        statusText = 'NYTT';
        statusColor = Colors.purple;
        break;
      case AlertStatus.acknowledged:
        statusText = 'PÅGÅENDE';
        statusColor = Colors.blue;
        break;
      case AlertStatus.resolved:
        statusText = 'ÅTGÄRDAT';
        statusColor = Colors.green;
        break;
      case AlertStatus.false_alarm:
        statusText = 'FALSKT LARM';
        statusColor = Colors.grey;
        break;
    }

    // Set colors based on alert type
    switch (alert.alertType) {
      case AlertType.red:
        cardColor = Colors.red.shade50;
        iconColor = Colors.red;
        textColor = Colors.red.shade700;
        priorityText = 'HÖG PRIORITET';
        break;
      case AlertType.yellow:
        cardColor = Colors.orange.shade50;
        iconColor = Colors.orange;
        textColor = Colors.orange.shade700;
        priorityText = 'LÅG PRIORITET';
        break;
      case AlertType.unknown:
        cardColor = Colors.grey.shade100;
        iconColor = Colors.grey;
        textColor = Colors.grey.shade700;
        priorityText = 'OKÄND';
        break;
    }

    // Extract store name from topic if available
    String storeName = alert.storeId;
    if (storeName == 'unassigned') {
      storeName = 'Ej tilldelad';
    }

    // Format date and time
    final date = alert.pressedAt;
    final formattedDate = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    final formattedTime = '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}:${date.second.toString().padLeft(2, '0')}';

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: cardColor,
      elevation: alert.alertType == AlertType.red ? 4 : 1,
      child: ListTile(
        leading: Stack(
          alignment: Alignment.center,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: iconColor,
              size: 36,
            ),
            if (alert.alertType == AlertType.red && alert.status == AlertStatus.active)
              Positioned.fill(
                child: TweenAnimationBuilder<double>(
                  tween: Tween<double>(begin: 0.5, end: 1.0),
                  duration: const Duration(milliseconds: 800),
                  builder: (context, value, child) {
                    return Opacity(
                      opacity: value,
                      child: Icon(
                        Icons.warning_amber_rounded,
                        color: Colors.red.withAlpha(128),
                        size: 36 * value,
                      ),
                    );
                  },
                  onEnd: () => setState(() {}),
                ),
              ),
          ],
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                'Butik: $storeName',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: textColor,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                statusText,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enhet: ${alert.buttonId}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text('Tid: $formattedDate $formattedTime'),
            if (alert.alertType != AlertType.unknown)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(50),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  priorityText,
                  style: TextStyle(
                    color: textColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                ),
              ),
          ],
        ),
        isThreeLine: true,
        onTap: () => _viewAlertDetails(alert.id),
      ),
    );
  }


}
