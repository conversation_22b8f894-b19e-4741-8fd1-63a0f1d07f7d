import 'package:flutter/material.dart';
import '../../models/store.dart';
import '../../models/iot_device.dart';
import '../../services/store_service.dart';

class StoreDetailScreen extends StatefulWidget {
  final bool isNewStore;
  final Store? store;
  
  const StoreDetailScreen({
    super.key,
    required this.isNewStore,
    this.store,
  });
  
  @override
  State<StoreDetailScreen> createState() => _StoreDetailScreenState();
}

class _StoreDetailScreenState extends State<StoreDetailScreen> {
  final _formKey = GlobalKey<FormState>();
  final StoreService _storeService = StoreService();
  
  late TextEditingController _nameController;
  late TextEditingController _addressController;
  late TextEditingController _cityController;
  late TextEditingController _postalCodeController;
  late TextEditingController _contactPersonController;
  late TextEditingController _contactPhoneController;
  late TextEditingController _contactEmailController;
  
  List<IoTDevice> _devices = [];
  
  @override
  void initState() {
    super.initState();
    
    // Initiera kontroller med befintliga värden eller tomma strängar
    final store = widget.store;
    _nameController = TextEditingController(text: store?.name ?? '');
    _addressController = TextEditingController(text: store?.address ?? '');
    _cityController = TextEditingController(text: store?.city ?? '');
    _postalCodeController = TextEditingController(text: store?.postalCode ?? '');
    _contactPersonController = TextEditingController(text: store?.contactPerson ?? '');
    _contactPhoneController = TextEditingController(text: store?.contactPhone ?? '');
    _contactEmailController = TextEditingController(text: store?.contactEmail ?? '');
    
    // Ladda enheter om det är en befintlig butik
    if (!widget.isNewStore && store != null) {
      _devices = _storeService.getDevicesForStore(store.id);
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _postalCodeController.dispose();
    _contactPersonController.dispose();
    _contactPhoneController.dispose();
    _contactEmailController.dispose();
    super.dispose();
  }
  
  void _saveStore() {
    if (!_formKey.currentState!.validate()) return;
    
    final store = widget.isNewStore
        ? Store(
            id: _storeService.generateId(),
            name: _nameController.text,
            address: _addressController.text,
            city: _cityController.text,
            postalCode: _postalCodeController.text,
            contactPerson: _contactPersonController.text,
            contactPhone: _contactPhoneController.text,
            contactEmail: _contactEmailController.text,
          )
        : widget.store!.copyWith(
            name: _nameController.text,
            address: _addressController.text,
            city: _cityController.text,
            postalCode: _postalCodeController.text,
            contactPerson: _contactPersonController.text,
            contactPhone: _contactPhoneController.text,
            contactEmail: _contactEmailController.text,
          );
    
    final success = widget.isNewStore
        ? _storeService.addStore(store)
        : _storeService.updateStore(store);
    
    if (success) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.isNewStore
                ? 'Butik skapad'
                : 'Butik uppdaterad',
          ),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ett fel uppstod'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  void _addDevice() {
    if (widget.isNewStore) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Spara butiken först innan du lägger till enheter'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    // Här skulle vi normalt visa en dialog för att lägga till en ny enhet
    // För demo skapar vi bara en ny enhet direkt
    final newDevice = IoTDevice(
      id: 'device-${_storeService.generateId()}',
      name: 'LarmKnapp-Ny',
      storeId: widget.store!.id,
      batteryLevel: 100,
      lastActive: DateTime.now(),
    );
    
    final success = _storeService.addDevice(newDevice);
    
    if (success) {
      setState(() {
        _devices = _storeService.getDevicesForStore(widget.store!.id);
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ny enhet tillagd'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ett fel uppstod'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isNewStore ? 'Ny butik' : 'Redigera butik'),
        actions: [
          if (!widget.isNewStore)
            IconButton(
              icon: const Icon(Icons.add_circle_outline),
              onPressed: _addDevice,
              tooltip: 'Lägg till enhet',
            ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveStore,
            tooltip: 'Spara',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Butiksinformation
              const Text(
                'Butiksinformation',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Namn
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Butiksnamn *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Ange butiksnamn';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // Adress
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'Adress *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Ange adress';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // Postnummer och stad
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: _postalCodeController,
                      decoration: const InputDecoration(
                        labelText: 'Postnummer *',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Ange postnummer';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 3,
                    child: TextFormField(
                      controller: _cityController,
                      decoration: const InputDecoration(
                        labelText: 'Stad *',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Ange stad';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              
              // Kontaktinformation
              const Text(
                'Kontaktinformation',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Kontaktperson
              TextFormField(
                controller: _contactPersonController,
                decoration: const InputDecoration(
                  labelText: 'Kontaktperson',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              
              // Telefon
              TextFormField(
                controller: _contactPhoneController,
                decoration: const InputDecoration(
                  labelText: 'Telefon',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              
              // E-post
              TextFormField(
                controller: _contactEmailController,
                decoration: const InputDecoration(
                  labelText: 'E-post',
                  border: OutlineInputBorder(),
                ),
              ),
              
              // Enheter (endast för befintliga butiker)
              if (!widget.isNewStore) ...[
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Enheter',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton.icon(
                      icon: const Icon(Icons.add),
                      label: const Text('Lägg till enhet'),
                      onPressed: _addDevice,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                
                // Lista över enheter
                if (_devices.isEmpty)
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(
                      child: Text('Inga enheter tillagda'),
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _devices.length,
                    itemBuilder: (context, index) {
                      final device = _devices[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: Icon(
                            Icons.device_hub,
                            color: device.isActive
                                ? Colors.green
                                : Colors.grey,
                          ),
                          title: Text(device.name),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('ID: ${device.id}'),
                              Row(
                                children: [
                                  Icon(
                                    Icons.battery_full,
                                    size: 16,
                                    color: device.isLowBattery
                                        ? Colors.red
                                        : Colors.green,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Batteri: ${device.batteryLevel}%',
                                    style: TextStyle(
                                      color: device.isLowBattery
                                          ? Colors.red
                                          : null,
                                      fontWeight: device.isLowBattery
                                          ? FontWeight.bold
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () {
                              // Här skulle vi normalt visa en dialog för att redigera enheten
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Redigera enhet (kommer snart)'),
                                ),
                              );
                            },
                          ),
                          isThreeLine: true,
                        ),
                      );
                    },
                  ),
              ],
              
              const SizedBox(height: 24),
              
              // Spara-knapp
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveStore,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    widget.isNewStore ? 'SKAPA BUTIK' : 'SPARA ÄNDRINGAR',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
