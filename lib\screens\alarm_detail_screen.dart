import 'package:flutter/material.dart';
import '../models/alarm.dart';
import '../models/alarm_report.dart';
import '../models/user.dart';
import '../services/alarm_service.dart';
import '../services/auth_service.dart';
import '../services/user_service.dart';
import '../widgets/home_button.dart';

class AlarmDetailScreen extends StatefulWidget {
  final String alarmId;

  const AlarmDetailScreen({super.key, required this.alarmId});

  @override
  State<AlarmDetailScreen> createState() => _AlarmDetailScreenState();
}

class _AlarmDetailScreenState extends State<AlarmDetailScreen> {
  final AlarmService _alarmService = AlarmService();
  final AuthService _authService = AuthService();
  final UserService _userService = UserService();
  final TextEditingController _reportController = TextEditingController();

  List<User> _activeGuards = [];
  User? _selectedGuard;

  @override
  void initState() {
    super.initState();
    _loadActiveGuards();
    // Force refresh of reports from Firestore
    _refreshReports();
  }

  // Method to refresh reports from Firestore
  void _refreshReports() {
    // This will trigger a fetch from Firestore in the background
    _alarmService.getReportsForAlarm(widget.alarmId);
    // Update UI after a short delay to allow fetch to complete
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {});
      }
    });
  }

  void _loadActiveGuards() {
    setState(() {
      _activeGuards = _userService.getActiveGuards();
    });
  }

  @override
  void dispose() {
    _reportController.dispose();
    super.dispose();
  }

  Future<void> _submitReport() async {
    if (_reportController.text.isEmpty) return;

    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Sparar rapport...'),
          duration: Duration(seconds: 1),
        ),
      );
    }

    final currentUser = _authService.currentUser;

    final report = AlarmReport(
      id: _alarmService.generateId(),
      alarmId: widget.alarmId,
      timestamp: DateTime.now(),
      content: _reportController.text,
      authorName: currentUser?.name ?? 'Användare',
    );

    try {
      await _alarmService.addReport(report, userId: currentUser?.id, userName: currentUser?.name);
      _reportController.clear();

      if (mounted) {
        // Refresh reports from Firestore
        _refreshReports();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Rapport sparad i databasen'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Kunde inte spara rapport: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _assignToGuard() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tilldela larm'),
        content: DropdownButtonFormField<User>(
          value: _selectedGuard,
          hint: const Text('Välj vakt'),
          items: _activeGuards.map((guard) {
            return DropdownMenuItem<User>(
              value: guard,
              child: Text(guard.name),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedGuard = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Avbryt'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_selectedGuard != null) {
                final guardName = _selectedGuard!.name;
                final guardId = _selectedGuard!.id;
                Navigator.pop(context);

                // Store context for later use
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                // Show loading indicator
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Tilldelar larm...'),
                    duration: Duration(seconds: 1),
                  ),
                );

                // Assign the alarm to the guard
                final success = await _userService.assignAlarmToGuard(widget.alarmId, guardId);

                if (mounted) {
                  setState(() {});

                  // Show success or error message
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        success
                          ? 'Larm tilldelat till $guardName'
                          : 'Kunde inte tilldela larm'
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Tilldela'),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getStatusText(AlarmStatus status, {DateTime? timestamp}) {
    switch (status) {
      case AlarmStatus.new_:
        // Check if the alarm is older than 15 minutes
        if (timestamp != null) {
          final fifteenMinutesAgo = DateTime.now().subtract(const Duration(minutes: 15));
          if (timestamp.isBefore(fifteenMinutesAgo)) {
            // If older than 15 minutes, don't show "NYTT"
            return '';
          }
        }
        return 'NYTT';
      case AlarmStatus.ongoing:
        return 'PÅGÅENDE';
      case AlarmStatus.resolved:
        return 'ÅTGÄRDAT';
    }
  }

  Widget _buildStatusBadge(AlarmStatus status, {DateTime? timestamp}) {
    Color color;
    String text = _getStatusText(status, timestamp: timestamp);

    switch (status) {
      case AlarmStatus.new_:
        color = Colors.red;
        break;
      case AlarmStatus.ongoing:
        color = Colors.blue;
        break;
      case AlarmStatus.resolved:
        color = Colors.green;
        break;
    }

    // If text is empty (for older new alarms), return an empty container
    if (text.isEmpty) {
      return Container();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final alarm = _alarmService.getAlarmById(widget.alarmId);

    if (alarm == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Larmdetaljer')),
        body: const Center(child: Text('Larmet hittades inte')),
      );
    }

    final reports = _alarmService.getReportsForAlarm(widget.alarmId);

    // Get the keyboard height to adjust padding
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Larmdetaljer'),
        actions: const [
          HomeButton(),
        ],
      ),
      // Use a SingleChildScrollView to make the content scrollable when keyboard appears
      body: SingleChildScrollView(
        // Add padding at the bottom when keyboard is visible
        padding: EdgeInsets.only(
          left: 16.0,
          right: 16.0,
          top: 16.0,
          bottom: bottomInset > 0 ? bottomInset + 16.0 : 16.0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              color: alarm.type == AlarmType.red ? Colors.red.shade100 : Colors.orange.shade100,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.warning_amber_rounded,
                          color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          alarm.type == AlarmType.red ? 'AKUT LARM' : 'VARNINGSLARM',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    Text(
                      'Larm ID: ${alarm.id}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Enhet: ${alarm.deviceId}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    Text(
                      'Plats: ${alarm.location}',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'Tid: ${_formatDateTime(alarm.timestamp)}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Text(
                          'Status: ',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        _buildStatusBadge(alarm.status, timestamp: alarm.timestamp),
                      ],
                    ),

                    // Visa vem som hanterar larmet
                    if (alarm.assignedUserName != null) ...[
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          const Icon(Icons.person, size: 16, color: Colors.blue),
                          const SizedBox(width: 4),
                          const Text(
                            'Hanteras av: ',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          Text(
                            alarm.assignedUserName!,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],

                    // Visa statushistorik om det finns
                    if (alarm.statusHistory.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      ExpansionTile(
                        title: const Text(
                          'Händelsehistorik',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        children: [
                          ...alarm.statusHistory.map((change) {
                            return ListTile(
                              dense: true,
                              leading: const Icon(Icons.history, size: 16),
                              title: Text(
                                change.note ?? 'Status ändrad från ${_getStatusText(change.fromStatus)} till ${_getStatusText(change.toStatus)}',
                              ),
                              subtitle: Text(
                                '${change.userName ?? 'System'} - ${_formatDateTime(change.timestamp)}',
                                style: const TextStyle(fontSize: 12),
                              ),
                            );
                          }),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Statusändringsalternativ
            if (alarm.status != AlarmStatus.resolved) ...[
              // Tilldela larm till vakt (endast för admin)
              if (_authService.isAdmin && alarm.assignedUserId == null)
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.person_add),
                        label: const Text(
                          'TILLDELA TILL VAKT',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        onPressed: _assignToGuard,
                      ),
                    ),
                  ],
                ),

              if (_authService.isAdmin && alarm.assignedUserId == null)
                const SizedBox(height: 8),

              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.play_arrow),
                      label: const Text(
                        'MARKERA SOM PÅGÅENDE',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      onPressed: alarm.status == AlarmStatus.ongoing
                          ? null
                          : () async {
                              final currentUser = _authService.currentUser;

                              // Show loading indicator
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Markerar som pågående...'),
                                  duration: Duration(seconds: 1),
                                ),
                              );

                              await _alarmService.markAlarmAsOngoing(
                                alarm.id,
                                userId: currentUser?.id,
                                userName: currentUser?.name,
                              );

                              if (mounted) {
                                setState(() {});
                              }
                            },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.check_circle),
                      label: const Text(
                        'MARKERA SOM ÅTGÄRDAT',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      onPressed: () async {
                        final currentUser = _authService.currentUser;

                        // Show loading indicator
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Markerar som åtgärdat...'),
                            duration: Duration(seconds: 1),
                          ),
                        );

                        await _alarmService.markAlarmAsHandled(
                          alarm.id,
                          userId: currentUser?.id,
                          userName: currentUser?.name,
                        );

                        if (mounted) {
                          setState(() {});
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 24),
            const Text(
              'Skriv rapport om larmet:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _reportController,
              maxLines: 4, // Reduced from 5 to avoid overflow
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Beskriv vad som hände och vilka åtgärder som vidtogs...',
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.save),
                    label: const Text(
                      'SPARA RAPPORT',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: _submitReport,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            if (reports.isNotEmpty) ...[
              const Text(
                'Tidigare rapporter:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              // Use a fixed height SizedBox instead of Expanded
              SizedBox(
                height: 200, // Fixed height for reports list
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: reports.length,
                  itemBuilder: (context, index) {
                    final report = reports[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Rapport från ${report.authorName} - ${_formatDateTime(report.timestamp)}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Text(report.content),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ] else ...[
              const Text('Inga rapporter än'),
            ],
          ],
        ),
      ),
    );
  }
}
