{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\larmknapp_app\\android\\app\\.cxx\\Debug\\4b6y6g3o\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\larmknapp_app\\android\\app\\.cxx\\Debug\\4b6y6g3o\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}