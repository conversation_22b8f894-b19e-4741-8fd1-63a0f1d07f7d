import 'package:flutter/material.dart';
import '../models/alarm.dart';
import '../screens/alert_queue_screen.dart';
import 'auth_service.dart';
import 'audio_service.dart';
import 'package:flutter/services.dart';

/// A helper class for showing dialogs from anywhere in the app
/// without requiring a BuildContext from the widget tree
class DialogHelper {
  static final DialogHelper _instance = DialogHelper._internal();
  final AudioService _audioService = AudioService();

  factory DialogHelper() {
    return _instance;
  }

  DialogHelper._internal();

  /// Shows an alarm notification by navigating to a new screen
  void showAlarmNotification(Alarm alarm) {
    debugPrint("DIALOG HELPER: Showing notification screen for alarm ${alarm.id}");

    // Play alarm sound immediately
    _audioService.playAlarmSound(alarm.type);

    // Vibrate the device if possible
    HapticFeedback.heavyImpact();

    // Schedule this on the next frame to ensure we're on the main UI thread
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Get the navigator state
      final navigatorState = AuthService.navigatorKey.currentState;

      if (navigatorState == null) {
        debugPrint("DIALOG HELPER: No navigator state available to show notification screen");

        // Try again after a short delay
        Future.delayed(const Duration(milliseconds: 500), () {
          final retryNavigatorState = AuthService.navigatorKey.currentState;
          if (retryNavigatorState != null) {
            debugPrint("DIALOG HELPER: Got navigator state on retry, showing notification screen");
            _navigateToAlertScreen(retryNavigatorState, alarm);
          } else {
            debugPrint("DIALOG HELPER: Still no navigator state available after retry");
          }
        });

        return;
      }

      _navigateToAlertScreen(navigatorState, alarm);
    });
  }

  /// Navigate to the alert screen
  void _navigateToAlertScreen(NavigatorState navigatorState, Alarm alarm) {
    // Push the alert queue screen on top of the current screen
    navigatorState.push(
      MaterialPageRoute(
        builder: (context) => AlertQueueScreen(alarm: alarm),
        fullscreenDialog: true, // Make it a fullscreen dialog for better visibility
      ),
    );

    // Play sound again after a short delay to ensure it gets attention
    Future.delayed(const Duration(milliseconds: 500), () {
      _audioService.playAlarmSound(alarm.type);
      HapticFeedback.heavyImpact();
    });
  }


}
