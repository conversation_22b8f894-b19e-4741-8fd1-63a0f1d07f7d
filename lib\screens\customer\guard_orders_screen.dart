import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/guard_order.dart';
import '../../services/store_service.dart';
import '../../services/auth_service.dart';
import '../../utils/responsive_layout.dart';
import '../../widgets/home_button.dart';
import 'create_guard_order_screen.dart';
import 'guard_order_detail_screen.dart';

class GuardOrdersScreen extends StatefulWidget {
  final String storeId;

  const GuardOrdersScreen({
    super.key,
    required this.storeId,
  });

  @override
  State<GuardOrdersScreen> createState() => _GuardOrdersScreenState();
}

class _GuardOrdersScreenState extends State<GuardOrdersScreen> {
  final _storeService = StoreService();
  final _authService = AuthService();

  List<GuardOrder> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  void _loadOrders() {
    setState(() {
      _isLoading = true;
    });

    try {
      final orders = _storeService.getGuardOrdersForStore(widget.storeId);
      // Sortera beställningarna efter datum (nyaste först)
      orders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _orders = orders;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  Future<void> _createNewOrder() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateGuardOrderScreen(
          storeId: widget.storeId,
        ),
      ),
    );

    if (result != null) {
      _loadOrders();
    }
  }

  Future<void> _viewOrderDetails(GuardOrder order) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GuardOrderDetailScreen(
          orderId: order.id,
        ),
      ),
    );

    if (result == true) {
      _loadOrders();
    }
  }

  Future<void> _cancelOrder(GuardOrder order) async {
    // Visa bekräftelsedialog
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Avbryt beställning'),
        content: const Text('Är du säker på att du vill avbryta denna beställning?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Nej'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Ja, avbryt'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    // Avbryt beställningen
    final success = _storeService.cancelGuardOrder(order.id);

    if (success) {
      _loadOrders();

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Beställningen har avbrutits'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Kunde inte avbryta beställningen'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final store = _storeService.getStoreById(widget.storeId);
    final storeName = store?.name ?? 'Okänd butik';

    return Scaffold(
      appBar: AppBar(
        title: Text('Vaktbeställningar - $storeName'),
        actions: const [
          HomeButton(),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _orders.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.security_outlined,
                        size: 64,
                        color: Colors.blue,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Inga beställningar',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Du har inte gjort några vaktbeställningar än.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: _createNewOrder,
                        icon: const Icon(Icons.add),
                        label: const Text('Skapa beställning'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: () async {
                    _loadOrders();
                  },
                  child: ListView.builder(
                    padding: ResponsiveLayout.getAdaptivePadding(context),
                    itemCount: _orders.length,
                    itemBuilder: (context, index) {
                      final order = _orders[index];

                      return Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: InkWell(
                          onTap: () => _viewOrderDetails(order),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Beställningsrubrik
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(4),
                                    topRight: Radius.circular(4),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.security,
                                      color: order.statusColor,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            order.guardTypeText,
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: order.statusColor,
                                            ),
                                          ),
                                          Text(
                                            'Beställd: ${DateFormat('yyyy-MM-dd').format(order.createdAt)}',
                                            style: const TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: order.statusColor,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        order.statusText.toUpperCase(),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Beställningsinnehåll
                              Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(Icons.calendar_today, size: 16),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Datum: ${DateFormat('yyyy-MM-dd').format(order.requestedDate)}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        const Icon(Icons.access_time, size: 16),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Tid: ${order.startTime} - ${order.endTime}',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        const Icon(Icons.people, size: 16),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Antal: ${order.numberOfGuards} ${order.numberOfGuards == 1 ? 'person' : 'personer'}',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),

                                    // Visa feedback om det finns
                                    if (order.adminFeedback != null && order.adminFeedback!.isNotEmpty)
                                      Container(
                                        margin: const EdgeInsets.only(top: 8),
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade100,
                                          borderRadius: BorderRadius.circular(4),
                                          border: Border.all(color: Colors.grey.shade300),
                                        ),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Feedback från admin:',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              order.adminFeedback!,
                                              style: const TextStyle(
                                                fontSize: 12,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                    const SizedBox(height: 8),

                                    // Knappar
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        // Visa avbryt-knapp endast för väntande eller godkända beställningar
                                        if (order.status == OrderStatus.pending || order.status == OrderStatus.approved)
                                          TextButton.icon(
                                            icon: const Icon(Icons.cancel),
                                            label: const Text('Avbryt'),
                                            onPressed: () => _cancelOrder(order),
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors.red,
                                            ),
                                          ),
                                        const SizedBox(width: 8),
                                        TextButton.icon(
                                          icon: const Icon(Icons.visibility),
                                          label: const Text('Visa detaljer'),
                                          onPressed: () => _viewOrderDetails(order),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewOrder,
        tooltip: 'Skapa ny beställning',
        child: const Icon(Icons.add),
      ),
    );
  }
}
