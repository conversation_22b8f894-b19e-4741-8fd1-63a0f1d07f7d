import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/alert.dart';
import '../services/alert_service.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../services/user_service.dart';
import '../models/user.dart';
import '../widgets/home_button.dart';

class AlertDetailScreen extends StatefulWidget {
  final String alertId;

  const AlertDetailScreen({super.key, required this.alertId});

  @override
  State<AlertDetailScreen> createState() => _AlertDetailScreenState();
}

class _AlertDetailScreenState extends State<AlertDetailScreen> {
  final AlertService _alertService = AlertService();
  final AuthService _authService = AuthService();
  final UserService _userService = UserService();
  final DatabaseService _databaseService = DatabaseService();
  final TextEditingController _reportController = TextEditingController();

  Alert? _alert;
  bool _isLoading = true;
  List<User> _activeGuards = [];
  User? _selectedGuard;
  StreamSubscription? _alertSubscription;

  @override
  void initState() {
    super.initState();
    _setupAlertListener();
    _loadActiveGuards();
  }

  void _setupAlertListener() {
    setState(() {
      _isLoading = true;
    });

    // Listen to the specific alert document
    _alertSubscription = _databaseService.alertsCollection
        .doc(widget.alertId)
        .snapshots()
        .listen((snapshot) {
      if (mounted) {
        if (snapshot.exists) {
          final data = snapshot.data() as Map<String, dynamic>;
          setState(() {
            _alert = Alert.fromMap({
              'id': snapshot.id,
              ...data,
            });
            _isLoading = false;
          });
        } else {
          // Document doesn't exist (may have been deleted)
          setState(() {
            _alert = null;
            _isLoading = false;
          });

          // Show a message and navigate back after a short delay
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Larmet har tagits bort från databasen'),
              duration: Duration(seconds: 3),
            ),
          );

          // Navigate back after a short delay
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              Navigator.of(context).pop();
            }
          });
        }
      }
    }, onError: (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading alert: $e')),
        );
      }
    });
  }

  void _loadActiveGuards() {
    setState(() {
      _activeGuards = _userService.getActiveGuards();
    });
  }

  Future<void> _submitReport() async {
    if (_reportController.text.isEmpty) return;
    if (_alert == null) return;

    final currentUser = _authService.currentUser;
    if (currentUser == null) return;

    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Sparar rapport...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Generate a unique report ID
      final reportId = FirebaseFirestore.instance.collection('reports').doc().id;

      // Create report data with the generated ID
      final reportData = {
        'id': reportId,
        'alertId': widget.alertId,
        'authorId': currentUser.id,
        'authorName': currentUser.name,
        'content': _reportController.text,
        'createdAt': Timestamp.now(),
      };

      // Use a transaction to ensure both operations succeed or fail together
      await FirebaseFirestore.instance.runTransaction((transaction) async {
        // Add report to Firestore reports collection
        transaction.set(
          _databaseService.reportsCollection.doc(reportId),
          reportData
        );

        // Also update the alert document with the report
        transaction.update(
          _databaseService.alertsCollection.doc(widget.alertId),
          {
            'reports': FieldValue.arrayUnion([reportData]),
          }
        );
      });

      // Clear text field
      _reportController.clear();

      // Alert will be updated automatically via the stream

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Rapport sparad')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving report: $e')),
        );
      }
    }
  }

  Future<void> _updateAlertStatus(AlertStatus status) async {
    if (_alert == null) return;

    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Uppdaterar status...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Update alert status
      final success = await _alertService.updateAlertStatus(
        widget.alertId,
        status,
        note: 'Status updated manually',
      );

      if (success) {
        // Alert will be updated automatically via the stream

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Status uppdaterad')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Kunde inte uppdatera status')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating status: $e')),
        );
      }
    }
  }

  void _assignToGuard() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tilldela larm'),
        content: DropdownButtonFormField<User>(
          value: _selectedGuard,
          hint: const Text('Välj vakt'),
          items: _activeGuards.map((guard) {
            return DropdownMenuItem<User>(
              value: guard,
              child: Text(guard.name),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedGuard = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Avbryt'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_selectedGuard != null) {
                Navigator.pop(context);
                await _assignAlert(_selectedGuard!);
              }
            },
            child: const Text('Tilldela'),
          ),
        ],
      ),
    );
  }

  Future<void> _assignAlert(User guard) async {
    if (_alert == null) return;

    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tilldelar larm...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Update alert with assigned guard
      await _databaseService.updateAlert(widget.alertId, {
        'handledBy': guard.id,
        'handlerName': guard.name,
        'status': 'acknowledged',
        'statusHistory': FieldValue.arrayUnion([
          {
            'timestamp': Timestamp.now(),
            'fromStatus': _alertStatusToString(_alert!.status),
            'toStatus': 'acknowledged',
            'userId': _authService.currentUser?.id,
            'userName': _authService.currentUser?.name,
            'note': 'Assigned to ${guard.name}',
          }
        ]),
      });

      // Alert will be updated automatically via the stream

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Larm tilldelat till ${guard.name}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error assigning alert: $e')),
        );
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _reportController.dispose();
    _alertSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Larmdetaljer'),
          actions: const [HomeButton()],
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_alert == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Larmdetaljer'),
          actions: const [HomeButton()],
        ),
        body: const Center(child: Text('Larmet hittades inte')),
      );
    }

    // Determine colors based on alert type
    Color cardColor;
    Color iconColor;
    String priorityText;

    switch (_alert!.alertType) {
      case AlertType.red:
        cardColor = Colors.red.shade100;
        iconColor = Colors.red;
        priorityText = 'HÖG PRIORITET';
        break;
      case AlertType.yellow:
        cardColor = Colors.orange.shade100;
        iconColor = Colors.orange;
        priorityText = 'LÅG PRIORITET';
        break;
      case AlertType.unknown:
        cardColor = Colors.grey.shade100;
        iconColor = Colors.grey;
        priorityText = 'OKÄND';
        break;
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Larmdetaljer'),
        actions: const [HomeButton()],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Alert details card
            Card(
              color: cardColor,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.warning_amber_rounded,
                          color: iconColor,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          priorityText,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            color: iconColor,
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    Text(
                      'Larm ID: ${_alert!.id}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Enhet: ${_alert!.buttonId}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    Text(
                      'Butik: ${_alert!.storeId}',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'Tid: ${_formatDateTime(_alert!.pressedAt)}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    if (_alert!.payload != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Meddelande: ${_alert!.payload}',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Text(
                          'Status: ',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        _buildStatusBadge(_alert!.status),
                      ],
                    ),

                    // Show who is handling the alert
                    if (_alert!.handlerName != null) ...[
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          const Icon(Icons.person, size: 16, color: Colors.blue),
                          const SizedBox(width: 4),
                          const Text(
                            'Hanteras av: ',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          Text(
                            _alert!.handlerName!,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],

                    // Show status history
                    if (_alert!.statusHistory.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      ExpansionTile(
                        title: const Text(
                          'Händelsehistorik',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        children: [
                          ..._alert!.statusHistory.map((change) {
                            return ListTile(
                              dense: true,
                              leading: const Icon(Icons.history, size: 16),
                              title: Text(
                                change.note ?? 'Status ändrad från ${_getStatusText(change.fromStatus)} till ${_getStatusText(change.toStatus)}',
                              ),
                              subtitle: Text(
                                '${change.userName ?? 'System'} - ${_formatDateTime(change.timestamp)}',
                                style: const TextStyle(fontSize: 12),
                              ),
                            );
                          }),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Status change options
            if (_alert!.status != AlertStatus.resolved && _alert!.status != AlertStatus.false_alarm) ...[
              // Assign alert to guard (admin only)
              if (_authService.isAdmin && _alert!.handledBy == null)
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.person_add),
                        label: const Text(
                          'TILLDELA TILL VAKT',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        onPressed: _assignToGuard,
                      ),
                    ),
                  ],
                ),

              if (_authService.isAdmin && _alert!.handledBy == null)
                const SizedBox(height: 8),

              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.play_arrow),
                      label: const Text(
                        'MARKERA SOM PÅGÅENDE',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      onPressed: _alert!.status == AlertStatus.acknowledged
                          ? null
                          : () => _updateAlertStatus(AlertStatus.acknowledged),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.check_circle),
                      label: const Text(
                        'MARKERA SOM ÅTGÄRDAT',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      onPressed: () => _updateAlertStatus(AlertStatus.resolved),
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 24),

            // Report section
            const Text(
              'Skriv rapport om larmet:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _reportController,
              maxLines: 5,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Beskriv vad som hände och vilka åtgärder som vidtogs...',
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.save),
                    label: const Text(
                      'SPARA RAPPORT',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: _submitReport,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Previous reports
            if (_alert != null && _alert!.reports.isNotEmpty) ...[
              const Text(
                'Tidigare rapporter:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _alert!.reports.length,
                  itemBuilder: (context, index) {
                    final report = _alert!.reports[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Rapport från ${report['authorName']} - ${_formatDateTime((report['createdAt'] as Timestamp).toDate())}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Text(report['content']),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ] else ...[
              const Text('Inga rapporter än'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(AlertStatus status) {
    Color color;
    String text = _getStatusText(status);

    switch (status) {
      case AlertStatus.active:
        color = Colors.red;
        break;
      case AlertStatus.acknowledged:
        color = Colors.blue;
        break;
      case AlertStatus.resolved:
        color = Colors.green;
        break;
      case AlertStatus.false_alarm:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  String _getStatusText(AlertStatus status) {
    switch (status) {
      case AlertStatus.active:
        return 'NYTT';
      case AlertStatus.acknowledged:
        return 'PÅGÅENDE';
      case AlertStatus.resolved:
        return 'ÅTGÄRDAT';
      case AlertStatus.false_alarm:
        return 'FALSKT LARM';
    }
  }

  String _alertStatusToString(AlertStatus status) {
    switch (status) {
      case AlertStatus.active:
        return 'active';
      case AlertStatus.acknowledged:
        return 'acknowledged';
      case AlertStatus.resolved:
        return 'resolved';
      case AlertStatus.false_alarm:
        return 'false_alarm';
    }
  }
}
