# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# Node.js related
node_modules/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Firebase secrets and configuration
serviceAccountKey.json
lib/firebase_options.dart
android/app/google-services.json
ios/Runner/GoogleService-Info.plist

# Local development configuration
android/local.properties

# Environment and configuration files
.env
.env.local
.env.production
*.config.js

# Scripts with hardcoded credentials
create-admin-user.js
set-admin-claim.js
create-auth-user-only.js
