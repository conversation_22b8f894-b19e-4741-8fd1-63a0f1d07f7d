import 'package:cloud_firestore/cloud_firestore.dart';

enum UserRole { guard, admin, customer }
enum UserStatus { active, inactive, onLeave }

class User {
  final String id;
  final String username;
  final String name;
  final UserRole role;
  final String email;
  final String phoneNumber;
  final UserStatus status;
  final String position;
  final String department;
  final DateTime lastActive;
  final List<String> handledAlarmIds;
  final String? storeId; // ID for store (only for customers)
  final List<String> stores; // Store IDs this user has access to (for guards)
  final Map<String, dynamic>? customClaims; // Firebase Auth custom claims

  User({
    required this.id,
    required this.username,
    required this.name,
    required this.role,
    required this.email,
    this.phoneNumber = '',
    this.status = UserStatus.active,
    this.position = '',
    this.department = '',
    DateTime? lastActive,
    this.handledAlarmIds = const [],
    this.storeId,
    this.stores = const [],
    this.customClaims,
  }) : lastActive = lastActive ?? DateTime.now();

  bool get isAdmin => role == UserRole.admin;
  bool get isGuard => role == UserRole.guard;
  bool get isCustomer => role == UserRole.customer;
  bool get isActive => status == UserStatus.active;

  int get activeAlarmsCount => handledAlarmIds.length;

  User copyWith({
    String? id,
    String? username,
    String? name,
    UserRole? role,
    String? email,
    String? phoneNumber,
    UserStatus? status,
    String? position,
    String? department,
    DateTime? lastActive,
    List<String>? handledAlarmIds,
    String? storeId,
    List<String>? stores,
    Map<String, dynamic>? customClaims,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      name: name ?? this.name,
      role: role ?? this.role,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      status: status ?? this.status,
      position: position ?? this.position,
      department: department ?? this.department,
      lastActive: lastActive ?? this.lastActive,
      handledAlarmIds: handledAlarmIds ?? this.handledAlarmIds,
      storeId: storeId ?? this.storeId,
      stores: stores ?? this.stores,
      customClaims: customClaims ?? this.customClaims,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'name': name,
      'role': role.index,
      'email': email,
      'phoneNumber': phoneNumber,
      'status': status.index,
      'position': position,
      'department': department,
      'lastActive': Timestamp.fromDate(lastActive),
      'handledAlarmIds': handledAlarmIds,
      'storeId': storeId,
      'stores': stores,
      // Don't store customClaims in Firestore, they're managed by Firebase Auth
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      username: map['username'],
      name: map['name'],
      role: UserRole.values[map['role']],
      email: map['email'],
      phoneNumber: map['phoneNumber'] ?? '',
      status: map['status'] != null ? UserStatus.values[map['status']] : UserStatus.active,
      position: map['position'] ?? '',
      department: map['department'] ?? '',
      lastActive: map['lastActive'] != null
          ? (map['lastActive'] is Timestamp
              ? (map['lastActive'] as Timestamp).toDate()
              : map['lastActive'] is int
                  ? DateTime.fromMillisecondsSinceEpoch(map['lastActive'])
                  : DateTime.now())
          : DateTime.now(),
      handledAlarmIds: map['handledAlarmIds'] != null
          ? List<String>.from(map['handledAlarmIds'])
          : [],
      storeId: map['storeId'],
      stores: map['stores'] != null ? List<String>.from(map['stores']) : [],
      customClaims: map['customClaims'],
    );
  }
}
