import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/guard_order.dart';
import '../../services/store_service.dart';
import '../../utils/responsive_layout.dart';
import '../../widgets/home_button.dart';
import 'guard_order_admin_detail_screen.dart';

class ManageGuardOrdersScreen extends StatefulWidget {
  const ManageGuardOrdersScreen({super.key});

  @override
  State<ManageGuardOrdersScreen> createState() => _ManageGuardOrdersScreenState();
}

class _ManageGuardOrdersScreenState extends State<ManageGuardOrdersScreen> with SingleTickerProviderStateMixin {
  final _storeService = StoreService();

  late TabController _tabController;
  List<GuardOrder> _allOrders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadOrders();

    // Lägg till en timer för att uppdatera beställningarna regelbundet
    Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        _loadOrders();
      } else {
        timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadOrders() {
    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('Laddar alla beställningar...');
      final orders = _storeService.getAllGuardOrders();
      debugPrint('Antal beställningar: ${orders.length}');

      // Skriv ut alla beställningar för felsökning
      for (var i = 0; i < orders.length; i++) {
        final order = orders[i];
        debugPrint('Beställning $i: ID=${order.id}, Status=${order.status}, Feedback=${order.adminFeedback}');
      }

      // Sortera beställningarna efter datum (nyaste först)
      // Skapa en kopia av listan först för att undvika problem med oföränderliga listor
      final sortedOrders = List<GuardOrder>.from(orders);
      sortedOrders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _allOrders = sortedOrders;
        _isLoading = false;
      });

      debugPrint('Beställningar laddade och sorterade');
    } catch (e) {
      debugPrint('Fel vid laddning av beställningar: $e');
      setState(() {
        _allOrders = [];
        _isLoading = false;
      });
    }
  }

  List<GuardOrder> get _pendingOrders => _allOrders
      .where((order) => order.status == OrderStatus.pending)
      .toList();

  List<GuardOrder> get _approvedOrders => _allOrders
      .where((order) => order.status == OrderStatus.approved)
      .toList();

  List<GuardOrder> get _otherOrders => _allOrders
      .where((order) =>
          order.status == OrderStatus.rejected ||
          order.status == OrderStatus.completed ||
          order.status == OrderStatus.cancelled)
      .toList();

  Future<void> _viewOrderDetails(GuardOrder order) async {
    debugPrint('Visar detaljer för beställning: ${order.id}');
    debugPrint('Beställningsstatus före: ${order.status}');
    debugPrint('Feedback före: ${order.adminFeedback}');

    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GuardOrderAdminDetailScreen(
          orderId: order.id,
        ),
      ),
    );

    // Ladda alltid om beställningarna när vi kommer tillbaka från detaljskärmen
    debugPrint('Tillbaka från detaljskärmen för beställning ${order.id}');

    // Hämta den uppdaterade beställningen för att se om den har ändrats
    final updatedOrder = _storeService.getGuardOrderById(order.id);
    if (updatedOrder != null) {
      debugPrint('Beställningsstatus efter: ${updatedOrder.status}');
      debugPrint('Feedback efter: ${updatedOrder.adminFeedback}');

      if (updatedOrder.status != order.status ||
          updatedOrder.adminFeedback != order.adminFeedback) {
        debugPrint('Beställningen har uppdaterats, laddar om listan');
      } else {
        debugPrint('Ingen förändring i beställningen');
      }
    } else {
      debugPrint('Kunde inte hitta beställningen efter redigering');
    }

    // Ladda om beställningarna oavsett
    _loadOrders();
    debugPrint('Laddar om beställningar efter att ha visat detaljer för ${order.id}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hantera vaktbeställningar'),
        actions: const [
          HomeButton(),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: 'Väntande (${_pendingOrders.length})',
            ),
            Tab(
              text: 'Godkända (${_approvedOrders.length})',
            ),
            Tab(
              text: 'Övriga (${_otherOrders.length})',
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                // Väntande beställningar
                _buildOrdersList(_pendingOrders, 'väntande'),

                // Godkända beställningar
                _buildOrdersList(_approvedOrders, 'godkända'),

                // Övriga beställningar
                _buildOrdersList(_otherOrders, 'övriga'),
              ],
            ),
    );
  }

  Widget _buildOrdersList(List<GuardOrder> orders, String type) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.security_outlined,
              size: 64,
              color: Colors.blue,
            ),
            const SizedBox(height: 16),
            Text(
              'Inga $type beställningar',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Det finns inga $type vaktbeställningar att visa.',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadOrders();
      },
      child: ListView.builder(
        padding: ResponsiveLayout.getAdaptivePadding(context),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];

          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: InkWell(
              onTap: () => _viewOrderDetails(order),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Beställningsrubrik
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                      ),
                      border: Border.all(color: order.statusColor),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.security,
                          color: order.statusColor,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                order.storeName,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: order.statusColor,
                                ),
                              ),
                              Text(
                                'Beställd: ${DateFormat('yyyy-MM-dd').format(order.createdAt)}',
                                style: const TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: order.statusColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            order.statusText.toUpperCase(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Beställningsinnehåll
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.security, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'Typ: ${order.guardTypeText}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'Datum: ${DateFormat('yyyy-MM-dd').format(order.requestedDate)}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Icon(Icons.access_time, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'Tid: ${order.startTime} - ${order.endTime}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Icon(Icons.people, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'Antal: ${order.numberOfGuards} ${order.numberOfGuards == 1 ? 'person' : 'personer'}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),

                        // Visa feedback om det finns
                        if (order.adminFeedback != null && order.adminFeedback!.isNotEmpty)
                          Container(
                            margin: const EdgeInsets.only(top: 8),
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Din feedback:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  order.adminFeedback!,
                                  style: const TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        const SizedBox(height: 8),

                        // Knappar
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton.icon(
                              icon: const Icon(Icons.visibility),
                              label: const Text('Hantera'),
                              onPressed: () => _viewOrderDetails(order),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
