# How to Get a Firebase Service Account Key and Create an Admin User

## Getting a Service Account Key

Follow these steps to get a service account key for your Firebase project:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project (larmknapp-52953)
3. Click on the gear icon (⚙️) next to "Project Overview" to open Project settings
4. Go to the "Service accounts" tab
5. Click on "Generate new private key" button
6. Save the downloaded JSON file as `serviceAccountKey.json` in the same directory as the `create-admin-user.js` script

## Important Security Notes

- Keep your service account key secure and never commit it to version control
- The service account key grants administrative access to your Firebase project
- Delete the key file after you've created your admin user

## Editing the Admin User Script

Before running the script, edit the `create-admin-user.js` file to set your desired admin user details:

1. Open `create-admin-user.js` in a text editor
2. Find these lines near the top of the file:
   ```javascript
   const email = '<EMAIL>'; // CHANGE THIS TO YOUR DESIRED EMAIL
   const password = 'Admin123!'; // <PERSON>ANGE THIS TO YOUR DESIRED PASSWORD
   const displayName = 'Admin User'; // CHANGE THIS TO YOUR DESIRED NAME
   ```
3. Change these values to your desired email, password, and name
4. Save the file

## Running the Script

You can run the admin user creation script in two ways:

### Option 1: Using the Batch File

Simply run the `create-admin-user.bat` file by double-clicking it. This will:
1. Install the required dependencies
2. Prompt you to confirm you've completed the prerequisites
3. Run the script to create the admin user

### Option 2: Manual Installation and Execution

If you prefer to run the commands manually:

1. Open a command prompt in the directory containing the script
2. Install the required dependencies:
   ```
   npm install firebase-admin
   ```
3. Run the script:
   ```
   node create-admin-user.js
   ```

## What the Script Does

The script performs the following actions:

1. Creates a new user in Firebase Authentication with the email and password you specified
2. Sets custom claims on the user to give them admin privileges
3. Attempts to create a document in the Firestore 'users' collection with all the necessary fields

If the Firestore document creation is successful, you'll see a confirmation message and can proceed to log in to your app.

If the Firestore document creation fails (which might happen due to database configuration issues), the script will provide detailed instructions for manually creating the document.

## After Creating the Admin User

Once the script completes successfully:
1. You can log in to your app with the email and password you specified
2. The user will have admin privileges
3. Delete the `serviceAccountKey.json` file for security

## Manually Creating the Firestore Document (if needed)

If the script indicates that it couldn't create the Firestore document automatically, you'll need to:

1. Go to the Firebase Console > Firestore Database
2. Create a document in the "users" collection with the user ID provided by the script
3. Add all the fields as listed in the script output
4. For the "lastActive" field, use the Timestamp type and set it to the current time
5. For array fields (handledAlarmIds, stores), create empty arrays

## Troubleshooting

If you encounter any errors:

1. Make sure the `serviceAccountKey.json` file is in the same directory as the script
2. Check that you have installed all required dependencies
3. Ensure you have proper permissions in your Firebase project
4. If you get authentication errors, make sure your service account has the necessary permissions
5. If you encounter Firestore errors, check your database configuration in the Firebase Console
