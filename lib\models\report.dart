import 'package:cloud_firestore/cloud_firestore.dart';

enum ReportType { storeToAdmin, guardToAdmin }

class Report {
  final String id;
  final String authorId;
  final String storeId;
  final DateTime createdAt;
  final ReportType type;
  final String content;
  final String? attachmentUrl;

  Report({
    required this.id,
    required this.authorId,
    required this.storeId,
    required this.createdAt,
    required this.type,
    required this.content,
    this.attachmentUrl,
  });

  Report copyWith({
    String? id,
    String? authorId,
    String? storeId,
    DateTime? createdAt,
    ReportType? type,
    String? content,
    String? attachmentUrl,
  }) {
    return Report(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      storeId: storeId ?? this.storeId,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
      content: content ?? this.content,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'authorId': authorId,
      'storeId': storeId,
      'createdAt': Timestamp.fromDate(createdAt),
      'type': _typeToString(type),
      'content': content,
      'attachmentUrl': attachmentUrl,
    };
  }

  factory Report.fromMap(Map<String, dynamic> map) {
    return Report(
      id: map['id'] ?? '',
      authorId: map['authorId'] ?? '',
      storeId: map['storeId'] ?? '',
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      type: _stringToType(map['type']),
      content: map['content'] ?? '',
      attachmentUrl: map['attachmentUrl'],
    );
  }

  static ReportType _stringToType(String? type) {
    switch (type) {
      case 'store-to-admin':
        return ReportType.storeToAdmin;
      case 'guard-to-admin':
        return ReportType.guardToAdmin;
      default:
        return ReportType.storeToAdmin;
    }
  }

  static String _typeToString(ReportType type) {
    switch (type) {
      case ReportType.storeToAdmin:
        return 'store-to-admin';
      case ReportType.guardToAdmin:
        return 'guard-to-admin';
    }
  }
}
