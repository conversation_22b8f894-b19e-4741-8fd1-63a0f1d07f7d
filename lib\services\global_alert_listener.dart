import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/alarm.dart';
import '../models/alert.dart';
import '../screens/alert_queue_screen.dart';
import 'auth_service.dart';
import 'audio_service.dart';
import 'database_service.dart';

/// A service that listens for new alerts globally and shows notifications
/// regardless of where the user is in the app.
class GlobalAlertListener {
  static final GlobalAlertListener _instance = GlobalAlertListener._internal();
  final DatabaseService _databaseService = DatabaseService();
  final AudioService _audioService = AudioService();

  // Subscription for Firestore alerts
  StreamSubscription? _firestoreAlertsSubscription;

  // Flag to track if the listener is active
  bool _isListening = false;

  factory GlobalAlertListener() {
    return _instance;
  }

  GlobalAlertListener._internal();

  /// Start listening for new alerts
  void startListening() {
    // Don't start if already listening
    if (_isListening) {
      debugPrint("ALERT LISTENER: Already listening, not starting again");
      return;
    }

    debugPrint("ALERT LISTENER: Starting to listen for new alerts");

    // Set the flag
    _isListening = true;

    // Cancel any existing subscription
    _firestoreAlertsSubscription?.cancel();

    // First, check for any recent alerts that might have been missed
    // This helps ensure we don't miss alerts that were created while the app was starting
    _checkForRecentAlerts();

    // Set up a direct listener to Firestore for alerts and specifically detect new documents
    _firestoreAlertsSubscription = _databaseService.getAlertsStream().listen((snapshot) {
      debugPrint("ALERT LISTENER: Received snapshot with ${snapshot.docChanges.length} changes");

      // Process only new documents that were added
      for (var change in snapshot.docChanges) {
        // Only process documents that were added (not modified or removed)
        if (change.type == DocumentChangeType.added) {
          final doc = change.doc;
          final data = doc.data() as Map<String, dynamic>;

          // Get the timestamp to check if this is a very recent alert
          final pressedAt = (data['pressedAt'] as Timestamp).toDate();
          final now = DateTime.now();
          final difference = now.difference(pressedAt);

          // Only process very recent alerts (created in the last 120 seconds)
          // This helps avoid showing notifications for older alerts when the app starts
          // Increased to 120 seconds to ensure we don't miss any alerts
          if (difference.inSeconds <= 120) {
            debugPrint("ALERT LISTENER: Processing new alert ${doc.id} (${difference.inSeconds} seconds old)");

            // Convert the alert to an alarm
            final alert = Alert.fromMap({
              'id': doc.id,
              ...data,
            });

            // Create an alarm from the alert
            final alarm = _convertAlertToAlarm(alert);

            // Show the notification immediately without requiring user interaction
            // Navigate directly to the alert screen
            _showAlertScreen(alarm);
          } else {
            debugPrint("ALERT LISTENER: Skipping older alert ${doc.id} (${difference.inSeconds} seconds old)");
          }
        }
      }
    }, onError: (error) {
      debugPrint("ALERT LISTENER: Error in alerts stream: $error");

      // Attempt to restart the listener after a short delay
      Future.delayed(const Duration(seconds: 5), () {
        if (_isListening) {
          debugPrint("ALERT LISTENER: Attempting to restart after error");
          stopListening();
          startListening();
        }
      });
    });
  }

  /// Check for any recent alerts that might have been missed
  void _checkForRecentAlerts() {
    // Get alerts from the last 2 minutes
    _databaseService.getRecentAlertsStream().first.then((snapshot) {
      debugPrint("ALERT LISTENER: Checking for recent alerts, found ${snapshot.docs.length}");

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // Get the timestamp to check if this is a very recent alert
        final pressedAt = (data['pressedAt'] as Timestamp).toDate();
        final now = DateTime.now();
        final difference = now.difference(pressedAt);

        // Only process very recent alerts (created in the last 120 seconds)
        if (difference.inSeconds <= 120) {
          debugPrint("ALERT LISTENER: Found recent alert ${doc.id} (${difference.inSeconds} seconds old)");

          // Convert the alert to an alarm
          final alert = Alert.fromMap({
            'id': doc.id,
            ...data,
          });

          // Create an alarm from the alert
          final alarm = _convertAlertToAlarm(alert);

          // Show the notification immediately without requiring user interaction
          // Navigate directly to the alert screen
          _showAlertScreen(alarm);
        }
      }
    }).catchError((error) {
      debugPrint("ALERT LISTENER: Error checking for recent alerts: $error");
    });
  }

  /// Show the alert screen directly with multiple retry attempts
  void _showAlertScreen(Alarm alarm) {
    debugPrint("ALERT LISTENER: Showing alert screen for alarm ${alarm.id}");

    // Play alarm sound and vibrate for attention
    _audioService.playAlarmSound(alarm.type);
    HapticFeedback.heavyImpact();

    // Store the alarm in a static variable for emergency access
    _lastReceivedAlarm = alarm;

    // Try to navigate immediately
    _attemptNavigation(alarm, 1);
  }

  // Static variable to store the last received alarm for emergency access
  static Alarm? _lastReceivedAlarm;

  // Maximum number of retry attempts
  static const int _maxRetryAttempts = 5;

  /// Get the last received alarm (can be used from anywhere in the app)
  static Alarm? getLastReceivedAlarm() {
    return _lastReceivedAlarm;
  }

  /// Force navigation to the alert screen from anywhere in the app
  /// This can be called from any part of the app to show the alert screen
  static void forceNavigateToAlertScreen(Alarm alarm) {
    debugPrint("ALERT LISTENER: Force navigating to alert screen for alarm ${alarm.id}");

    // Store the alarm for emergency access
    _lastReceivedAlarm = alarm;

    // Get the instance and attempt navigation
    final instance = GlobalAlertListener();
    instance._attemptNavigation(alarm, 1);
  }

  /// Attempt to navigate to the alert screen with retry logic
  void _attemptNavigation(Alarm alarm, int attemptNumber) {
    debugPrint("ALERT LISTENER: Navigation attempt #$attemptNumber for alarm ${alarm.id}");

    // Get the navigator state
    final navigatorState = AuthService.navigatorKey.currentState;

    if (navigatorState == null) {
      debugPrint("ALERT LISTENER: No navigator state available on attempt #$attemptNumber");

      // If we haven't exceeded max retries, try again with exponential backoff
      if (attemptNumber < _maxRetryAttempts) {
        // Calculate delay with exponential backoff (500ms, 1000ms, 2000ms, etc.)
        final delay = Duration(milliseconds: 500 * attemptNumber);

        debugPrint("ALERT LISTENER: Will retry in ${delay.inMilliseconds}ms (attempt #${attemptNumber + 1})");

        Future.delayed(delay, () {
          _attemptNavigation(alarm, attemptNumber + 1);
        });
      } else {
        debugPrint("ALERT LISTENER: Max retry attempts reached. Using emergency navigation method.");
        _useEmergencyNavigation(alarm);
      }
      return;
    }

    // We have a navigator state, try to push the screen
    _pushAlertScreen(navigatorState, alarm);
  }

  /// Emergency navigation method as a last resort
  void _useEmergencyNavigation(Alarm alarm) {
    debugPrint("ALERT LISTENER: Using emergency navigation for alarm ${alarm.id}");

    // Force a rebuild of the entire app to get a fresh navigator
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // Try to directly navigate using the global key
        AuthService.navigatorKey.currentState?.pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => AlertQueueScreen(alarm: alarm),
            fullscreenDialog: true,
          ),
          (route) => false, // Remove all previous routes
        );

        // Play sound again
        _audioService.playAlarmSound(alarm.type);
        HapticFeedback.heavyImpact();
      } catch (e) {
        debugPrint("ALERT LISTENER: Emergency navigation failed: $e");

        // Try the overlay context method as a last resort
        _useOverlayContextNavigation(alarm);
      }
    });
  }

  /// Use the overlay context to navigate to the alert screen
  /// This is the most aggressive approach and should only be used as a last resort
  void _useOverlayContextNavigation(Alarm alarm) {
    debugPrint("ALERT LISTENER: Using overlay context navigation for alarm ${alarm.id}");

    // Get the overlay context from the navigator key
    final overlayContext = AuthService.navigatorKey.currentState?.overlay?.context;

    if (overlayContext != null) {
      debugPrint("ALERT LISTENER: Got overlay context, attempting navigation");

      // Use the overlay context to navigate
      Navigator.of(overlayContext, rootNavigator: true).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => AlertQueueScreen(alarm: alarm),
          fullscreenDialog: true,
        ),
        (route) => false, // Remove all previous routes
      );

      // Play sound again
      _audioService.playAlarmSound(alarm.type);
      HapticFeedback.heavyImpact();
    } else {
      debugPrint("ALERT LISTENER: No overlay context available, cannot navigate");

      // As an absolute last resort, try a different approach
      // This is very aggressive but it's better than not showing the alert
      WidgetsBinding.instance.addPostFrameCallback((_) {
        try {
          // Try to use a direct route replacement
          final navigatorState = AuthService.navigatorKey.currentState;
          if (navigatorState != null) {
            // Replace the current route with our alert screen
            navigatorState.pushReplacement(
              MaterialPageRoute(
                builder: (context) => AlertQueueScreen(alarm: alarm),
                fullscreenDialog: true,
              ),
            );
          }
        } catch (e) {
          debugPrint("ALERT LISTENER: Last resort navigation failed: $e");

          // Absolute last resort: show a dialog that can't be dismissed
          try {
            final context = AuthService.navigatorKey.currentContext;
            if (context != null) {
              // Store navigator state for later use
              final navState = AuthService.navigatorKey.currentState;

              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (dialogContext) => PopScope(
                  canPop: false, // Prevent back button from closing
                  child: AlertDialog(
                    title: Text(
                      alarm.type == AlarmType.red ? 'AKUT LARM!' : 'VARNINGSLARM!',
                      style: TextStyle(
                        color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Nytt larm har kommit in!'),
                        Text('Plats: ${alarm.location}'),
                        Text('Tid: ${alarm.timestamp.toString()}'),
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(dialogContext).pop();
                          navState?.push(
                            MaterialPageRoute(
                              builder: (context) => AlertQueueScreen(alarm: alarm),
                              fullscreenDialog: true,
                            ),
                          );
                        },
                        child: Text('VISA LARM'),
                      ),
                    ],
                  ),
                ),
              );
            }
          } catch (dialogError) {
            debugPrint("ALERT LISTENER: Even dialog failed: $dialogError");
          }
        }
      });
    }
  }

  /// Push the alert screen to the navigator
  void _pushAlertScreen(NavigatorState navigatorState, Alarm alarm) {
    // Push the alert queue screen on top of the current screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // First try to pop any current dialogs that might be blocking navigation
        navigatorState.popUntil((route) => route.isFirst);

        // Then push our alert screen
        navigatorState.push(
          MaterialPageRoute(
            builder: (context) => AlertQueueScreen(alarm: alarm),
            fullscreenDialog: true,
          ),
        );

        // Play sound again after a short delay to ensure it gets attention
        Future.delayed(const Duration(milliseconds: 500), () {
          _audioService.playAlarmSound(alarm.type);
          HapticFeedback.heavyImpact();
        });
      } catch (e) {
        debugPrint("ALERT LISTENER: Error pushing alert screen: $e");

        // If pushing fails, try the emergency method
        _useEmergencyNavigation(alarm);
      }
    });
  }

  /// Stop listening for new alerts
  void stopListening() {
    _firestoreAlertsSubscription?.cancel();
    _firestoreAlertsSubscription = null;
    _isListening = false;
  }

  /// Convert an Alert to an Alarm
  Alarm _convertAlertToAlarm(Alert alert) {
    return Alarm(
      id: alert.id,
      timestamp: alert.pressedAt,
      deviceId: alert.buttonId,
      location: alert.storeId, // Use storeId as location for now
      type: alert.alertType == AlertType.red ? AlarmType.red : AlarmType.yellow,
      status: AlarmStatus.new_,
    );
  }


}
