import 'package:flutter/material.dart';

class ResponsiveLayout {
  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < 600;

  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width >= 600 &&
      MediaQuery.of(context).size.width < 1200;

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= 1200;
      
  static double getScreenWidth(BuildContext context) =>
      MediaQuery.of(context).size.width;
      
  static double getScreenHeight(BuildContext context) =>
      MediaQuery.of(context).size.height;
      
  // Returnerar en relativ storlek baserad på skärmbredd
  static double getRelativeWidth(BuildContext context, double percentage) =>
      MediaQuery.of(context).size.width * percentage;
      
  // Returnerar en relativ storlek baserad på skärmhöjd
  static double getRelativeHeight(BuildContext context, double percentage) =>
      MediaQuery.of(context).size.height * percentage;
      
  // Returnerar en adaptiv fontsize baserad på skärmbredd
  static double getAdaptiveFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return baseFontSize * 0.8;
    } else if (screenWidth < 600) {
      return baseFontSize * 0.9;
    } else if (screenWidth < 900) {
      return baseFontSize;
    } else {
      return baseFontSize * 1.1;
    }
  }
  
  // Returnerar en adaptiv padding baserad på skärmstorlek
  static EdgeInsets getAdaptivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return const EdgeInsets.all(8.0);
    } else if (screenWidth < 600) {
      return const EdgeInsets.all(12.0);
    } else {
      return const EdgeInsets.all(16.0);
    }
  }
  
  // Returnerar en adaptiv storlek för ikoner
  static double getAdaptiveIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return baseSize * 0.8;
    } else if (screenWidth < 600) {
      return baseSize * 0.9;
    } else if (screenWidth < 900) {
      return baseSize;
    } else {
      return baseSize * 1.2;
    }
  }
}
