import 'package:flutter/material.dart';

/// Säkerhetstema för appen
/// 
/// Detta tema är designat för att förmedla säkerhet, professionalitet och pålitlighet.
/// Färgerna är valda för att representera säkerhet (mörkblå, mörkgrå) med accentfärger
/// för att indikera olika statusar (röd för varning, grön för säkert, etc.)
class SecurityTheme {
  // Primära färger
  static const Color primaryColor = Color(0xFF1A3A5A); // Mörkblå - förmedlar säkerhet och stabilitet
  static const Color secondaryColor = Color(0xFF2C5282); // Mellanblå - komplement till primärfärgen
  static const Color accentColor = Color(0xFFE53E3E); // Röd - för varningar och viktiga meddelanden
  
  // Bakgrundsfärger
  static const Color backgroundColor = Color(0xFFF7FAFC); // Ljusgrå bakgrund - ren och professionell
  static const Color cardColor = Colors.white; // Vit för kort - tydlig kontrast
  static const Color darkBackgroundColor = Color(0xFF2D3748); // Mörkgrå för vissa bakgrunder
  
  // Textfärger
  static const Color primaryTextColor = Color(0xFF1A202C); // Nästan svart - tydlig läsbarhet
  static const Color secondaryTextColor = Color(0xFF4A5568); // Mörkgrå - för sekundär text
  static const Color lightTextColor = Colors.white; // Vit text för mörka bakgrunder
  
  // Statusfärger
  static const Color successColor = Color(0xFF38A169); // Grön - för framgång/säkert
  static const Color warningColor = Color(0xFFDD6B20); // Orange - för varningar
  static const Color dangerColor = Color(0xFFE53E3E); // Röd - för fara/fel
  static const Color infoColor = Color(0xFF3182CE); // Blå - för information
  
  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF1A3A5A),
      Color(0xFF2C5282),
    ],
  );
  
  static const LinearGradient dangerGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE53E3E),
      Color(0xFFC53030),
    ],
  );
  
  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF38A169),
      Color(0xFF2F855A),
    ],
  );
  
  // Skuggor
  static List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 10,
      offset: const Offset(0, 4),
    ),
  ];
  
  static List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: Colors.black.withOpacity(0.2),
      blurRadius: 5,
      offset: const Offset(0, 2),
    ),
  ];
  
  // Avrundade hörn
  static const double borderRadius = 8.0;
  static BorderRadius roundedBorder = BorderRadius.circular(borderRadius);
  
  // Padding
  static const EdgeInsets cardPadding = EdgeInsets.all(16.0);
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(vertical: 12.0, horizontal: 24.0);
  
  // Skapa ThemeData för hela appen
  static ThemeData themeData() {
    return ThemeData(
      // Grundläggande färger
      primaryColor: primaryColor,
      primaryColorDark: primaryColor,
      primaryColorLight: secondaryColor,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        error: dangerColor,
        background: backgroundColor,
        surface: cardColor,
        onPrimary: lightTextColor,
        onSecondary: lightTextColor,
        onBackground: primaryTextColor,
        onSurface: primaryTextColor,
        onError: lightTextColor,
      ),
      
      // Bakgrundsfärg
      scaffoldBackgroundColor: backgroundColor,
      
      // Appbar
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: lightTextColor,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: lightTextColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
        iconTheme: IconThemeData(
          color: lightTextColor,
        ),
      ),
      
      // Kort
      cardTheme: CardTheme(
        color: cardColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0),
      ),
      
      // Knappar
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: lightTextColor,
          padding: buttonPadding,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          textStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: buttonPadding,
          textStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor),
          padding: buttonPadding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          textStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
      ),
      
      // Textfält
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: const BorderSide(color: secondaryColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: BorderSide(color: secondaryColor.withOpacity(0.5)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: const BorderSide(color: dangerColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        labelStyle: const TextStyle(color: secondaryTextColor),
        hintStyle: TextStyle(color: secondaryTextColor.withOpacity(0.7)),
      ),
      
      // Typografi
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: primaryTextColor,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: primaryTextColor,
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: primaryTextColor,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: primaryTextColor,
        ),
        titleLarge: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: primaryTextColor,
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: primaryTextColor,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: primaryTextColor,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: primaryTextColor,
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: primaryTextColor,
        ),
      ),
      
      // Divider
      dividerTheme: const DividerThemeData(
        color: Color(0xFFE2E8F0),
        thickness: 1,
        space: 24,
      ),
      
      // Ikoner
      iconTheme: const IconThemeData(
        color: primaryColor,
        size: 24,
      ),
      
      // Drawer
      drawerTheme: const DrawerThemeData(
        backgroundColor: cardColor,
        elevation: 2,
      ),
      
      // Snackbar
      snackBarTheme: SnackBarThemeData(
        backgroundColor: darkBackgroundColor,
        contentTextStyle: const TextStyle(color: lightTextColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        behavior: SnackBarBehavior.floating,
      ),
      
      // Dialoger
      dialogTheme: DialogTheme(
        backgroundColor: cardColor,
        elevation: 5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
      
      // Checkboxes och Switches
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor;
          }
          return Colors.transparent;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor;
          }
          return Colors.grey.shade400;
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor.withOpacity(0.5);
          }
          return Colors.grey.shade300;
        }),
      ),
      
      // Tabs
      tabBarTheme: const TabBarTheme(
        labelColor: primaryColor,
        unselectedLabelColor: secondaryTextColor,
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(
            width: 3.0,
            color: primaryColor,
          ),
        ),
      ),
      
      // Floating Action Button
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: lightTextColor,
        elevation: 4,
        shape: CircleBorder(),
      ),
    );
  }
  
  // Hjälpmetoder för att skapa anpassade knappstilar
  static ButtonStyle dangerButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: dangerColor,
      foregroundColor: lightTextColor,
      padding: buttonPadding,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      textStyle: const TextStyle(
        fontWeight: FontWeight.bold,
        letterSpacing: 0.5,
      ),
    );
  }
  
  static ButtonStyle successButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: successColor,
      foregroundColor: lightTextColor,
      padding: buttonPadding,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      textStyle: const TextStyle(
        fontWeight: FontWeight.bold,
        letterSpacing: 0.5,
      ),
    );
  }
  
  static ButtonStyle warningButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: warningColor,
      foregroundColor: lightTextColor,
      padding: buttonPadding,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      textStyle: const TextStyle(
        fontWeight: FontWeight.bold,
        letterSpacing: 0.5,
      ),
    );
  }
  
  // Anpassade kort
  static BoxDecoration cardDecoration() {
    return BoxDecoration(
      color: cardColor,
      borderRadius: roundedBorder,
      boxShadow: cardShadow,
    );
  }
  
  static BoxDecoration primaryCardDecoration() {
    return BoxDecoration(
      gradient: primaryGradient,
      borderRadius: roundedBorder,
      boxShadow: cardShadow,
    );
  }
  
  static BoxDecoration dangerCardDecoration() {
    return BoxDecoration(
      gradient: dangerGradient,
      borderRadius: roundedBorder,
      boxShadow: cardShadow,
    );
  }
  
  static BoxDecoration successCardDecoration() {
    return BoxDecoration(
      gradient: successGradient,
      borderRadius: roundedBorder,
      boxShadow: cardShadow,
    );
  }
}
