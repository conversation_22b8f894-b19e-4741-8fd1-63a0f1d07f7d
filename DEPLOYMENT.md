# Deployment Guide

This guide explains how to deploy the Firebase Functions and Firestore Security Rules for the Larmknapp App.

## Prerequisites

- Make sure you have the Firebase CLI installed:
  ```
  npm install -g firebase-tools
  ```

- Make sure you are logged in to Firebase:
  ```
  firebase login
  ```

## Deployment Options

### Option 1: Deploy Everything at Once

Run the `deploy_all.bat` script to deploy both Functions and Firestore Rules:

```
deploy_all.bat
```

### Option 2: Deploy Functions Only

Run the `deploy_functions.bat` script to deploy only the Firebase Functions:

```
deploy_functions.bat
```

### Option 3: Deploy Firestore Rules Only

Run the `deploy_rules.bat` script to deploy only the Firestore Security Rules:

```
deploy_rules.bat
```

## Manual Deployment

If you prefer to deploy manually, you can use the following commands:

### Deploy Functions

```
cd functions
npm install
cd ..
firebase deploy --only "functions"
```

### Deploy Firestore Rules

```
firebase deploy --only "firestore:rules"
```

### Deploy Firestore Indexes

```
firebase deploy --only "firestore:indexes"
```

### Deploy Everything

```
firebase deploy --only "functions,firestore"
```

## Testing the Deployment

After deployment, you can test the system by:

1. Sending a test MQTT message with payload "rod larm" or "gul larm"
2. Checking the Firebase console to see if an alert was created
3. Verifying that notifications were sent to the appropriate users

## Troubleshooting

### PowerShell Syntax

If you're using PowerShell, make sure to use quotes around comma-separated lists:

```
firebase deploy --only "functions,firestore"
```

### Function Deployment Errors

If you encounter errors during function deployment, check the Firebase Functions logs in the Firebase Console.

### Firestore Rules Errors

If you encounter errors with Firestore Rules, you can validate them before deployment:

```
firebase firestore:rules:validate
```

## Cloud Function Details

The following Cloud Functions are deployed:

1. **mqttWebhook**: Receives MQTT messages from IoT devices
2. **processMqttMessages**: Processes MQTT messages and creates alerts
3. **sendAlertNotifications**: Sends notifications when a new alert is created
4. **setUserClaims**: Sets custom claims on user tokens based on their role

## Firestore Security Rules

The Firestore Security Rules implement Role-Based Access Control (RBAC) for the app:

- **Admin** users have full access to all collections
- **Guard** users can only access data for their assigned stores
- **Store** users can only access data for their own store

## Firestore Indexes

The deployment also includes Firestore indexes for common queries:

- Alerts by store and status
- Alerts by store and timestamp
- Notifications by user and timestamp
- Buttons by store and status
- Requests by store and timestamp
- Shifts by guard and timestamp
- Reports by store and timestamp
- Activities by user and timestamp
