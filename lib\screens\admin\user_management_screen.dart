import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../services/user_service.dart';
import '../../widgets/home_button.dart';
import 'user_detail_screen.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final UserService _userService = UserService();
  final TextEditingController _searchController = TextEditingController();

  List<User> _users = [];
  List<User> _filteredUsers = [];

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadUsers() {
    setState(() {
      _users = _userService.getAllUsers();
      _filteredUsers = List.from(_users);
    });
  }

  void _filterUsers(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredUsers = List.from(_users);
      });
    } else {
      setState(() {
        _filteredUsers = _userService.searchUsers(query);
      });
    }
  }

  void _addNewUser() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const UserDetailScreen(isNewUser: true),
      ),
    ).then((_) {
      _loadUsers();
    });
  }

  void _editUser(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserDetailScreen(
          isNewUser: false,
          user: user,
        ),
      ),
    ).then((_) {
      _loadUsers();
    });
  }

  String _getUserStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return 'Aktiv';
      case UserStatus.inactive:
        return 'Inaktiv';
      case UserStatus.onLeave:
        return 'Ledig';
    }
  }

  Color _getUserStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Colors.green;
      case UserStatus.inactive:
        return Colors.red;
      case UserStatus.onLeave:
        return Colors.orange;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Räkna användare per roll och status
    final adminCount = _users.where((u) => u.isAdmin).length;
    final guardCount = _users.where((u) => u.isGuard).length;
    final activeCount = _users.where((u) => u.isActive).length;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Personalhantering'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addNewUser,
            tooltip: 'Lägg till ny användare',
          ),
          const HomeButton(),
        ],
      ),
      body: Column(
        children: [
          // Sökfält
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Sök personal',
                hintText: 'Ange namn, användarnamn eller avdelning...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterUsers('');
                        },
                      )
                    : null,
              ),
              onChanged: _filterUsers,
            ),
          ),

          // Statistik
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      'Totalt',
                      _users.length.toString(),
                      Icons.people,
                      Colors.blue,
                    ),
                    _buildStatItem(
                      'Administratörer',
                      adminCount.toString(),
                      Icons.admin_panel_settings,
                      Colors.purple,
                    ),
                    _buildStatItem(
                      'Vakter',
                      guardCount.toString(),
                      Icons.security,
                      Colors.green,
                    ),
                    _buildStatItem(
                      'Aktiva',
                      activeCount.toString(),
                      Icons.check_circle,
                      Colors.green,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Användarlista
          Expanded(
            child: _filteredUsers.isEmpty
                ? const Center(
                    child: Text(
                      'Inga användare hittades',
                      style: TextStyle(fontSize: 16),
                    ),
                  )
                : ListView.builder(
                    itemCount: _filteredUsers.length,
                    itemBuilder: (context, index) {
                      final user = _filteredUsers[index];

                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: user.isAdmin ? Colors.purple : Colors.blue,
                            child: Text(
                              user.name.substring(0, 1).toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          title: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  user.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: _getUserStatusColor(user.status),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  _getUserStatusText(user.status),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user.isAdmin ? 'Administratör' : 'Vakt',
                                style: TextStyle(
                                  color: user.isAdmin ? Colors.purple : Colors.blue,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text('${user.position}, ${user.department}'),
                              Text('E-post: ${user.email}'),
                            ],
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _editUser(user),
                            tooltip: 'Redigera användare',
                          ),
                          onTap: () => _editUser(user),
                          isThreeLine: true,
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewUser,
        tooltip: 'Lägg till ny användare',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}
