import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
// import 'package:file_picker/file_picker.dart';
import '../../models/guard_order.dart';
import '../../services/store_service.dart';
import '../../services/auth_service.dart';
import '../../services/file_service.dart';
import '../../utils/responsive_layout.dart';
import '../../widgets/attachment_list.dart';

class CreateGuardOrderScreen extends StatefulWidget {
  final String storeId;

  const CreateGuardOrderScreen({
    super.key,
    required this.storeId,
  });

  @override
  State<CreateGuardOrderScreen> createState() => _CreateGuardOrderScreenState();
}

class _CreateGuardOrderScreenState extends State<CreateGuardOrderScreen> {
  final _formKey = GlobalKey<FormState>();
  final _storeService = StoreService();
  final _authService = AuthService();

  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _startTime = const TimeOfDay(hour: 8, minute: 0);
  TimeOfDay _endTime = const TimeOfDay(hour: 16, minute: 0);
  GuardType _selectedGuardType = GuardType.entranceHost;
  int _numberOfGuards = 1;
  final _descriptionController = TextEditingController();

  final _fileService = FileService();
  List<String> _attachmentPaths = [];
  String? _securityGuardPermitPath;

  bool _isLoading = false;

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectStartTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _startTime,
    );
    if (picked != null && picked != _startTime) {
      setState(() {
        _startTime = picked;
        // Om sluttiden är före starttiden, sätt sluttiden till 8 timmar efter starttiden
        if (_timeToDouble(_endTime) <= _timeToDouble(_startTime)) {
          _endTime = TimeOfDay(
            hour: (_startTime.hour + 8) % 24,
            minute: _startTime.minute,
          );
        }
      });
    }
  }

  Future<void> _selectEndTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _endTime,
    );
    if (picked != null && picked != _endTime) {
      setState(() {
        _endTime = picked;
      });
    }
  }

  // Konvertera TimeOfDay till double för jämförelse
  double _timeToDouble(TimeOfDay time) {
    return time.hour + time.minute / 60.0;
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  // Välj bilagor
  Future<void> _pickAttachments() async {
    final result = await _fileService.pickMultipleFiles(
      type: 'custom',
      allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'txt'],
    );

    if (result != null) {
      final files = result.paths
          .where((path) => path != null)
          .map((path) => File(path!))
          .toList();

      if (files.isNotEmpty) {
        final savedPaths = await _fileService.saveFiles(files);

        setState(() {
          _attachmentPaths = [..._attachmentPaths, ...savedPaths];
        });
      }
    }
  }

  // Välj tillstånd för ordningsvakt
  Future<void> _pickSecurityGuardPermit() async {
    final result = await _fileService.pickFile(
      type: 'custom',
      allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'txt'],
    );

    if (result != null && result.files.isNotEmpty && result.files.first.path != null) {
      final file = File(result.files.first.path!);
      final savedPath = await _fileService.saveFile(file);

      if (savedPath != null) {
        setState(() {
          _securityGuardPermitPath = savedPath;
        });
      }
    }
  }

  // Ta bort en bilaga
  void _removeAttachment(String path) {
    setState(() {
      _attachmentPaths = _attachmentPaths.where((p) => p != path).toList();
    });
    _fileService.deleteFile(path);
  }

  // Ta bort tillstånd
  void _removePermit() {
    if (_securityGuardPermitPath != null) {
      _fileService.deleteFile(_securityGuardPermitPath!);
      setState(() {
        _securityGuardPermitPath = null;
      });
    }
  }

  Future<void> _submitOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Kontrollera om tillstånd krävs för ordningsvakt
    if (_selectedGuardType == GuardType.securityGuard && _securityGuardPermitPath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Du måste bifoga ett tillstånd för ordningsvakt'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final order = _storeService.createGuardOrder(
        storeId: widget.storeId,
        requestedDate: _selectedDate,
        startTime: _formatTimeOfDay(_startTime),
        endTime: _formatTimeOfDay(_endTime),
        guardType: _selectedGuardType,
        numberOfGuards: _numberOfGuards,
        description: _descriptionController.text.trim(),
        attachmentPaths: _attachmentPaths,
        securityGuardPermitPath: _securityGuardPermitPath,
      );

      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      // Visa bekräftelse och navigera tillbaka
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Beställningen har skickats'),
          backgroundColor: Colors.green,
        ),
      );

      Navigator.pop(context, order);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      // Visa felmeddelande
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Ett fel uppstod: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final store = _storeService.getStoreById(widget.storeId);
    final storeName = store?.name ?? 'Okänd butik';

    return Scaffold(
      appBar: AppBar(
        title: Text('Beställ vakt - $storeName'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: ResponsiveLayout.getAdaptivePadding(context),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Informationstext
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.info_outline, color: Colors.blue.shade700),
                                const SizedBox(width: 8),
                                Text(
                                  'Information',
                                  style: TextStyle(
                                    fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Här kan du beställa vakter till din butik. Fyll i formuläret nedan och skicka in din beställning. '
                              'Vi kommer att kontakta dig så snart som möjligt för att bekräfta din beställning.',
                              style: TextStyle(
                                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 14),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Datum och tid
                    Text(
                      'Datum och tid',
                      style: TextStyle(
                        fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Datum
                    ListTile(
                      title: const Text('Datum'),
                      subtitle: Text(DateFormat('yyyy-MM-dd').format(_selectedDate)),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () => _selectDate(context),
                    ),

                    // Starttid
                    ListTile(
                      title: const Text('Starttid'),
                      subtitle: Text(_formatTimeOfDay(_startTime)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () => _selectStartTime(context),
                    ),

                    // Sluttid
                    ListTile(
                      title: const Text('Sluttid'),
                      subtitle: Text(_formatTimeOfDay(_endTime)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () => _selectEndTime(context),
                    ),

                    const SizedBox(height: 16),

                    // Vakttyp och antal
                    Text(
                      'Vakttyp och antal',
                      style: TextStyle(
                        fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Vakttyp
                    DropdownButtonFormField<GuardType>(
                      decoration: const InputDecoration(
                        labelText: 'Typ av vakt',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedGuardType,
                      items: GuardType.values.map((type) {
                        String label;
                        switch (type) {
                          case GuardType.entranceHost:
                            label = 'Entrévärd';
                            break;
                          case GuardType.securityGuard:
                            label = 'Ordningsvakt';
                            break;
                          case GuardType.guard:
                            label = 'Väktare';
                            break;
                        }
                        return DropdownMenuItem<GuardType>(
                          value: type,
                          child: Text(label),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedGuardType = value;
                          });
                        }
                      },
                    ),

                    const SizedBox(height: 16),

                    // Antal vakter
                    Row(
                      children: [
                        const Text('Antal vakter:'),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Slider(
                            value: _numberOfGuards.toDouble(),
                            min: 1,
                            max: 10,
                            divisions: 9,
                            label: _numberOfGuards.toString(),
                            onChanged: (value) {
                              setState(() {
                                _numberOfGuards = value.round();
                              });
                            },
                          ),
                        ),
                        Container(
                          width: 40,
                          alignment: Alignment.center,
                          child: Text(
                            _numberOfGuards.toString(),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Beskrivning
                    Text(
                      'Beskrivning',
                      style: TextStyle(
                        fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Beskriv dina behov',
                        hintText: 'T.ex. särskilda krav, anledning till beställningen, etc.',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 4,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Vänligen ange en beskrivning';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 24),

                    // Bilagor
                    Text(
                      'Bilagor',
                      style: TextStyle(
                        fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Bifoga relevanta dokument',
                              style: TextStyle(
                                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 14),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // Lista med bilagor
                            if (_attachmentPaths.isNotEmpty)
                              Container(
                                margin: const EdgeInsets.only(bottom: 16),
                                child: AttachmentList(
                                  attachmentPaths: _attachmentPaths,
                                  onRemove: _removeAttachment,
                                ),
                              ),

                            ElevatedButton.icon(
                              onPressed: _pickAttachments,
                              icon: const Icon(Icons.attach_file),
                              label: const Text('Lägg till bilaga'),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Tillstånd för ordningsvakt (visas endast om ordningsvakt är vald)
                    if (_selectedGuardType == GuardType.securityGuard) ...[
                      const SizedBox(height: 24),

                      Text(
                        'Tillstånd för ordningsvakt',
                        style: TextStyle(
                          fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),

                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.warning, color: Colors.red.shade700),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'Tillstånd krävs för ordningsvakt',
                                      style: TextStyle(
                                        fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 14),
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red.shade700,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'För att beställa ordningsvakt måste du bifoga ett giltigt tillstånd från Polismyndigheten.',
                              ),
                              const SizedBox(height: 16),

                              // Visa tillstånd om det finns
                              if (_securityGuardPermitPath != null)
                                Container(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  child: AttachmentList(
                                    attachmentPaths: [_securityGuardPermitPath!],
                                    onRemove: (_) => _removePermit(),
                                  ),
                                ),

                              ElevatedButton.icon(
                                onPressed: _pickSecurityGuardPermit,
                                icon: const Icon(Icons.upload_file),
                                label: Text(_securityGuardPermitPath == null
                                  ? 'Ladda upp tillstånd'
                                  : 'Byt tillstånd'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _securityGuardPermitPath == null
                                    ? Colors.red
                                    : Colors.blue,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],

                    const SizedBox(height: 24),

                    // Skicka-knapp
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _submitOrder,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          'SKICKA BESTÄLLNING',
                          style: TextStyle(
                            fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 16),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
