{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"@google-cloud/firestore": "^7.3.0", "body-parser": "^1.20.2", "express": "^4.18.2", "firebase-admin": "^12.6.0", "firebase-functions": "^4.7.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}