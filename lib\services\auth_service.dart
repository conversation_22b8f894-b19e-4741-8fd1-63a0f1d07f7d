import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../models/user.dart';
import '../services/alarm_service.dart';
import '../services/iot_device_simulator.dart';
import '../services/firestore_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  final firebase_auth.FirebaseAuth _firebaseAuth = firebase_auth.FirebaseAuth.instance;
  final FirestoreService _firestoreService = FirestoreService();

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();

  User? _currentUser;
  final _authStateController = StreamController<User?>.broadcast();

  Stream<User?> get authStateChanges => _authStateController.stream;
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  bool get isGuard => _currentUser?.isGuard ?? false;
  bool get isCustomer => _currentUser?.isCustomer ?? false;

  // Initialize the service and listen to Firebase Auth changes
  void initialize() {
    _firebaseAuth.authStateChanges().listen(_onAuthStateChanged);
  }

  // Handle Firebase Auth state changes
  Future<void> _onAuthStateChanged(firebase_auth.User? firebaseUser) async {
    if (firebaseUser == null) {
      print('Firebase user is null, logging out');
      _currentUser = null;
      _authStateController.add(null);
      return;
    }

    print('Firebase auth state changed for user: ${firebaseUser.email}');

    try {
      // Get the ID token result to access custom claims
      final idTokenResult = await firebaseUser.getIdTokenResult(true);
      final customClaims = idTokenResult.claims;

      print('User token claims: ${customClaims.toString()}');

      // Get user data from Firestore
      final userDoc = await _firestoreService.getUser(firebaseUser.uid);
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        _currentUser = User.fromMap({
          'id': firebaseUser.uid,
          ...userData,
          'customClaims': customClaims,
        });
        print('User data loaded from Firestore: ${_currentUser?.email}');
        _authStateController.add(_currentUser);
      } else {
        // User exists in Firebase Auth but not in Firestore
        print('User exists in Firebase Auth but not in Firestore: ${firebaseUser.email}');

        // Create basic user data for new users
        final userDataToStore = {
          'username': firebaseUser.email?.split('@')[0] ?? 'user',
          'name': 'User',
          'role': UserRole.guard.index, // Default to guard role
          'email': firebaseUser.email ?? '',
          'phoneNumber': '',
          'stores': [], // Empty stores array for guards
        };

        // Store user data in Firestore
        await _firestoreService.setUser(
          firebaseUser.uid,
          userDataToStore,
        );

        // Create user object
        _currentUser = User.fromMap({
          'id': firebaseUser.uid,
          ...userDataToStore,
          'customClaims': customClaims,
        });
        _authStateController.add(_currentUser);
        print('Basic user data stored in Firestore for: ${firebaseUser.email}');
      }

      // Force token refresh if needed
      if (customClaims == null ||
          customClaims['role'] == null ||
          (customClaims['role'] != _roleToString(_currentUser!.role))) {
        print('Custom claims need to be updated, forcing token refresh');
        await firebaseUser.getIdToken(true);
      }
    } catch (e) {
      print('Error fetching user data: $e');
      _currentUser = null;
      _authStateController.add(null);
    }
  }

  // Helper method to convert UserRole enum to string
  String _roleToString(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'admin';
      case UserRole.guard:
        return 'guard';
      case UserRole.customer:
        return 'store';
    }
  }

  // No demo users - using Firebase Authentication only

  // Logga in användare
  Future<User?> login(String email, String password) async {
    try {
      // Sign in with Firebase Authentication
      await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Wait a moment for the auth state listener to update _currentUser
      await Future.delayed(const Duration(milliseconds: 500));

      // The auth state listener will update _currentUser
      return _currentUser;
    } catch (e) {
      // Log error but don't expose details to UI
      return null;
    }
  }

  // Globalt navigeringsnyckelvariabel för att kunna navigera utan kontext
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // Logga ut användare
  Future<void> logout() async {
    try {
      // Stoppa IoT-simulatorn först
      final iotSimulator = IoTDeviceSimulator();
      if (iotSimulator.isSimulating) {
        iotSimulator.stopSimulation();
      }

      // Vänta en kort stund för att säkerställa att simulatorn har stoppats
      await Future.delayed(const Duration(milliseconds: 100));

      // Rensa alla larm-prenumerationer
      final alarmService = AlarmService();
      alarmService.clearSubscriptions();

      // Vänta en kort stund för att säkerställa att prenumerationerna har rensats
      await Future.delayed(const Duration(milliseconds: 100));

      // Sign out from Firebase
      await _firebaseAuth.signOut();

      // The auth state listener will update _currentUser and notify listeners

      // Vänta en kort stund för att säkerställa att alla lyssnare har hunnit reagera
      await Future.delayed(const Duration(milliseconds: 300));

      // Navigera till inloggningsskärmen om navigatorKey är tillgänglig
      if (navigatorKey.currentState != null) {
        navigatorKey.currentState!.pushNamedAndRemoveUntil('/login', (route) => false);
      }
    } catch (e) {
      print('Logout error: $e');

      // Om något går fel, försök ändå att rensa användaren
      try {
        await _firebaseAuth.signOut();
      } catch (_) {}

      _currentUser = null;
      _authStateController.add(null);

      // Försök navigera till inloggningsskärmen även vid fel
      if (navigatorKey.currentState != null) {
        navigatorKey.currentState!.pushNamedAndRemoveUntil('/login', (route) => false);
      }
    }
  }

  // Hämta alla användare (endast för admin)
  Future<List<User>> getAllUsers() async {
    if (!isAdmin) return [];

    try {
      final querySnapshot = await _firestoreService.usersCollection.get();
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return User.fromMap({
          'id': doc.id,
          ...data,
        });
      }).toList();
    } catch (e) {
      print('Error getting users: $e');
      return [];
    }
  }

  // Create a new user (admin only)
  Future<bool> createUser(String email, String password, Map<String, dynamic> userData) async {
    if (!isAdmin) return false;

    try {
      // Create user in Firebase Auth
      final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Store user data in Firestore
      await _firestoreService.setUser(
        userCredential.user!.uid,
        userData,
      );

      return true;
    } catch (e) {
      print('Error creating user: $e');
      return false;
    }
  }

  // Stäng resurser
  void dispose() {
    _authStateController.close();
  }
}
