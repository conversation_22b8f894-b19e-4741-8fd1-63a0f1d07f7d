# Manual Firestore Document Creation Guide

After creating a user in Firebase Authentication and setting the admin custom claims, you need to manually create a document in Firestore for this user. This guide will walk you through the process.

## Prerequisites

- You have already created a user in Firebase Authentication
- You have set the admin custom claims for this user
- You have the user's UID (User ID)

## Steps to Create the Firestore Document

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project (larmknapp-52953)
3. In the left sidebar, click on "Firestore Database"
4. Make sure you're using the default database (you can switch databases using the dropdown at the top of the page if needed)
5. Click on "Start collection" (if the database is empty) or "Add collection" (if there are already collections)
6. Enter "users" as the Collection ID and click "Next"
7. For the Document ID, enter the UID of the user you created (e.g., 5GyAIYVyvtcVe6MRBJAfV6JFdiM2)
8. Add the following fields:

| Field Name | Type | Value |
|------------|------|-------|
| username | string | Choose a username (e.g., "admin") |
| name | string | Admin User |
| role | number | 1 |
| email | string | The email you used to create the user |
| phoneNumber | string | (can be empty) |
| status | number | 0 |
| position | string | Administrator |
| department | string | Administration |
| lastActive | timestamp | Current time |
| handledAlarmIds | array | (leave empty) |
| stores | array | (leave empty) |

9. Click "Save" to create the document

## Verifying the Setup

After creating the Firestore document:

1. Try logging in to your app with the email and password you used to create the user
2. You should now have admin privileges in the app
3. You should be able to create more users through the app's admin interface

## Troubleshooting

If you encounter issues:

1. Verify that the user exists in Firebase Authentication
2. Check that the custom claims were set correctly
3. Ensure the Firestore document has the correct ID (it must match the user's UID)
4. Make sure all required fields are present in the Firestore document
5. Check that the role field is set to 1 (admin)

Remember that the Cloud Function `setUserClaims` should automatically set the custom claims based on the role field in the Firestore document, but we've also set them manually to ensure they're applied.
