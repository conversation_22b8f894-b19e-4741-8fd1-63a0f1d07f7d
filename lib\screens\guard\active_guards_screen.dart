import 'dart:async';
import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../models/alarm.dart';
import '../../services/user_service.dart';
import '../../services/alarm_service.dart';
import '../../services/auth_service.dart';
import '../../widgets/home_button.dart';

class ActiveGuardsScreen extends StatefulWidget {
  const ActiveGuardsScreen({super.key});

  @override
  State<ActiveGuardsScreen> createState() => _ActiveGuardsScreenState();
}

class _ActiveGuardsScreenState extends State<ActiveGuardsScreen> {
  final UserService _userService = UserService();
  final AlarmService _alarmService = AlarmService();
  final AuthService _authService = AuthService();

  List<User> _activeGuards = [];
  StreamSubscription<Alarm>? _alarmSubscription;

  // Keep track of the currently viewed guard for real-time updates
  User? _currentlyViewedGuard;
  List<Alarm> _currentGuardAlarms = [];

  @override
  void initState() {
    super.initState();
    _loadActiveGuards();
    _setupAlarmListener();
  }

  @override
  void dispose() {
    _alarmSubscription?.cancel();
    super.dispose();
  }

  void _setupAlarmListener() {
    // Listen to alarm updates from the AlarmService
    _alarmSubscription = _alarmService.alarmStream.listen((alarm) {
      if (mounted) {
        // When an alarm is updated or deleted, refresh the active guards
        setState(() {
          _loadActiveGuards();

          // If we're currently viewing a guard's details, update their alarms too
          if (_currentlyViewedGuard != null) {
            _updateCurrentGuardAlarms();
          }
        });
      }
    });
  }

  void _loadActiveGuards() {
    setState(() {
      _activeGuards = _userService.getActiveGuards();
    });
  }

  void _updateCurrentGuardAlarms() {
    if (_currentlyViewedGuard != null) {
      _currentGuardAlarms = _userService.getAlarmsHandledByGuard(_currentlyViewedGuard!.id);
      setState(() {});
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _viewGuardDetails(User guard) {
    // Store the currently viewed guard for real-time updates
    _currentlyViewedGuard = guard;
    // Get initial alarms
    _currentGuardAlarms = _userService.getAlarmsHandledByGuard(guard.id);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildGuardDetailsSheet(guard),
    ).then((_) {
      // Clear the currently viewed guard when the modal is closed
      _currentlyViewedGuard = null;
    });
  }

  Widget _buildGuardDetailsSheet(User guard) {
    // Use the _currentGuardAlarms for real-time updates
    final alarms = _currentGuardAlarms;
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.3,
      maxChildSize: 0.9,
      expand: false,
      builder: (context, scrollController) {
        return SingleChildScrollView(
          controller: scrollController,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.blue,
                    child: Text(
                      guard.name.substring(0, 1).toUpperCase(),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          guard.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          guard.position,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'AKTIV',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Divider(),

              // Kontaktinformation
              const Text(
                'Kontaktinformation',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.email, 'E-post', guard.email),
              _buildInfoRow(Icons.phone, 'Telefon', guard.phoneNumber),
              _buildInfoRow(Icons.business, 'Avdelning', guard.department),
              _buildInfoRow(
                Icons.access_time,
                'Senast aktiv',
                _formatDateTime(guard.lastActive),
              ),

              const SizedBox(height: 16),
              const Divider(),

              // Hanterade larm
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Hanterade larm',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${alarms.length} st',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Show a message when there are no alarms
              if (alarms.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: Text(
                      'Inga aktiva larm',
                      style: TextStyle(
                        fontSize: 16,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                )
              else
                // Use a ListView.builder for efficient rendering of alarms
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: alarms.length,
                  itemBuilder: (context, index) {
                    final alarm = alarms[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      color: alarm.type == AlarmType.red
                          ? Colors.red.shade50
                          : Colors.orange.shade50,
                      child: ListTile(
                        leading: Icon(
                          Icons.warning_amber_rounded,
                          color: alarm.type == AlarmType.red
                              ? Colors.red
                              : Colors.orange,
                        ),
                        title: Text(
                          alarm.type == AlarmType.red
                              ? 'AKUT LARM'
                              : 'VARNINGSLARM',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: alarm.type == AlarmType.red
                                ? Colors.red
                                : Colors.orange,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Plats: ${alarm.location}'),
                            Text('Tid: ${_formatDateTime(alarm.timestamp)}'),
                            Text(
                              'Status: ${alarm.status == AlarmStatus.new_ ? 'Nytt' : alarm.status == AlarmStatus.ongoing ? 'Pågående' : 'Åtgärdat'}',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: alarm.status == AlarmStatus.new_
                                    ? Colors.red
                                    : alarm.status == AlarmStatus.ongoing
                                        ? Colors.blue
                                        : Colors.green,
                              ),
                            ),
                          ],
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.arrow_forward_ios),
                          onPressed: () {
                            Navigator.pop(context);
                            Navigator.pushNamed(
                              context,
                              '/alarm_detail',
                              arguments: alarm.id,
                            );
                          },
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.blue),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Aktiva vakter'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadActiveGuards,
            tooltip: 'Uppdatera',
          ),
          const HomeButton(),
        ],
      ),
      // Use a StreamBuilder to listen for alarm updates
      body: StreamBuilder<Alarm>(
        stream: _alarmService.alarmStream,
        builder: (context, snapshot) {
          // This will rebuild whenever a new alarm is received
          return _activeGuards.isEmpty
              ? const Center(
                  child: Text(
                    'Inga aktiva vakter',
                    style: TextStyle(fontSize: 16),
                  ),
                )
              : ListView.builder(
                  itemCount: _activeGuards.length,
                  itemBuilder: (context, index) {
                    final guard = _activeGuards[index];
                    // Get alarms in real-time
                    final alarms = _userService.getAlarmsHandledByGuard(guard.id);

                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.blue,
                          child: Text(
                            guard.name.substring(0, 1).toUpperCase(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        title: Text(
                          guard.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('${guard.position}, ${guard.department}'),
                            Row(
                              children: [
                                Icon(
                                  Icons.warning_amber_rounded,
                                  size: 16,
                                  color: alarms.isEmpty ? Colors.grey : Colors.red,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Hanterar: ${alarms.length} larm',
                                  style: TextStyle(
                                    fontWeight: alarms.isNotEmpty
                                        ? FontWeight.bold
                                        : null,
                                    color: alarms.isNotEmpty ? Colors.red : null,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () => _viewGuardDetails(guard),
                        isThreeLine: true,
                      ),
                    );
                  },
                );
          }
        ),
    );
  }
}
