import 'package:cloud_firestore/cloud_firestore.dart';

enum ShiftStatus { active, ended }

class Shift {
  final String id;
  final String guardId;
  final List<String> storeIds;
  final DateTime startTime;
  final DateTime endTime;
  final ShiftStatus status;

  Shift({
    required this.id,
    required this.guardId,
    required this.storeIds,
    required this.startTime,
    required this.endTime,
    this.status = ShiftStatus.active,
  });

  bool get isActive => status == ShiftStatus.active;
  bool get isEnded => status == ShiftStatus.ended;

  Shift copyWith({
    String? id,
    String? guardId,
    List<String>? storeIds,
    DateTime? startTime,
    DateTime? endTime,
    ShiftStatus? status,
  }) {
    return Shift(
      id: id ?? this.id,
      guardId: guardId ?? this.guardId,
      storeIds: storeIds ?? this.storeIds,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'guardId': guardId,
      'storeIds': storeIds,
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'status': _statusToString(status),
    };
  }

  factory Shift.fromMap(Map<String, dynamic> map) {
    return Shift(
      id: map['id'] ?? '',
      guardId: map['guardId'] ?? '',
      storeIds: map['storeIds'] != null 
          ? List<String>.from(map['storeIds']) 
          : [],
      startTime: map['startTime'] is Timestamp 
          ? (map['startTime'] as Timestamp).toDate() 
          : DateTime.now(),
      endTime: map['endTime'] is Timestamp 
          ? (map['endTime'] as Timestamp).toDate() 
          : DateTime.now().add(const Duration(hours: 8)),
      status: _stringToStatus(map['status']),
    );
  }

  static ShiftStatus _stringToStatus(String? status) {
    switch (status) {
      case 'active':
        return ShiftStatus.active;
      case 'ended':
        return ShiftStatus.ended;
      default:
        return ShiftStatus.active;
    }
  }

  static String _statusToString(ShiftStatus status) {
    switch (status) {
      case ShiftStatus.active:
        return 'active';
      case ShiftStatus.ended:
        return 'ended';
    }
  }
}
