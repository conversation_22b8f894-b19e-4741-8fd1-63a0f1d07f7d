import 'dart:async';
import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../models/alarm.dart';
import '../../services/auth_service.dart';
import '../../services/store_service.dart';
import '../../services/alarm_service.dart';
import '../../utils/responsive_layout.dart';
import '../../theme/security_theme.dart';
import '../../widgets/security_logo.dart';
import '../alarm_screen.dart';
import '../report_screen.dart';
import 'device_status_screen.dart';
import 'store_search_screen.dart';
import 'active_guards_screen.dart';

class GuardDashboard extends StatefulWidget {
  const GuardDashboard({super.key});

  @override
  State<GuardDashboard> createState() => _GuardDashboardState();
}

class _GuardDashboardState extends State<GuardDashboard> {
  final AuthService _authService = AuthService();
  final StoreService _storeService = StoreService();
  final AlarmService _alarmService = AlarmService();

  User? _currentUser;
  int _activeAlarms = 0;
  int _lowBatteryCount = 0;

  StreamSubscription? _alarmSubscription;

  @override
  void initState() {
    super.initState();

    // Säkrare hantering av currentUser
    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      _currentUser = currentUser;
      _loadData();

      // Lyssna på nya larm
      _alarmSubscription = _alarmService.alarmStream.listen((_) {
        if (mounted) {
          _loadData();
        }
      });
    } else {
      // Hantera fallet när användaren inte är inloggad
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _authService.logout(); // Säkerställ att användaren loggas ut
      });
    }
  }

  @override
  void dispose() {
    // Avsluta prenumerationen när widgeten tas bort
    _alarmSubscription?.cancel();
    super.dispose();
  }

  void _loadData() {
    final alarms = _alarmService.alarms;
    final devices = _storeService.getAllDevices();

    setState(() {
      _activeAlarms = alarms.where((a) => !a.isHandled).length;
      _lowBatteryCount = devices.where((d) => d.isLowBattery).length;
    });
  }

  void _logout() {
    showDialog(
      context: context,
      barrierDismissible: false, // Förhindra att användaren stänger dialogen genom att klicka utanför
      builder: (context) => AlertDialog(
        title: const Text('Logga ut'),
        content: const Text('Är du säker på att du vill logga ut?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Avbryt'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Visa laddningsindikator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );

              // Avsluta prenumerationen innan utloggning
              _alarmSubscription?.cancel();
              _alarmSubscription = null;

              // Stäng dialogerna
              Navigator.pop(context); // Stäng laddningsindikatorn
              Navigator.pop(context); // Stäng bekräftelsedialogen

              // Logga ut användaren
              await _authService.logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logga ut'),
          ),
        ],
      ),
    );
  }

  void _navigateToScreen(Widget screen) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    ).then((_) => _loadData());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const SecurityLogo(
              size: 36,
              showText: false,
            ),
            const SizedBox(width: 12),
            const Text('Vaktpanel'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Logga ut',
          ),
        ],
        elevation: 2,
        shadowColor: Colors.black.withAlpha(50),
      ),
      body: SingleChildScrollView(
        padding: ResponsiveLayout.getAdaptivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Välkomstsektion
            Container(
              decoration: BoxDecoration(
                gradient: SecurityTheme.primaryGradient,
                borderRadius: SecurityTheme.roundedBorder,
                boxShadow: SecurityTheme.cardShadow,
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(50),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.security,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Välkommen, ${_currentUser?.name ?? "Användare"}',
                            style: TextStyle(
                              fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 20),
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            'Vakt',
                            style: TextStyle(
                              fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 16),
                              color: Colors.white.withAlpha(200),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Varningar
            if (_activeAlarms > 0 || _lowBatteryCount > 0) ...[
              Text(
                'Varningar',
                style: TextStyle(
                  fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              if (_activeAlarms > 0)
                _buildWarningCard(
                  'Aktiva larm',
                  '$_activeAlarms ${_activeAlarms == 1 ? 'larm kräver' : 'larm kräver'} åtgärd',
                  Icons.warning,
                  SecurityTheme.dangerColor,
                  () => _navigateToScreen(const AlarmScreen()),
                ),

              if (_lowBatteryCount > 0)
                _buildWarningCard(
                  'Lågt batteri',
                  '$_lowBatteryCount ${_lowBatteryCount == 1 ? 'enhet har' : 'enheter har'} lågt batteri',
                  Icons.battery_alert,
                  SecurityTheme.warningColor,
                  () => _navigateToScreen(const DeviceStatusScreen()),
                ),

              const SizedBox(height: 24),
            ],

            // Snabbåtkomst
            Text(
              'Snabbåtkomst',
              style: TextStyle(
                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Larm',
                    Icons.warning,
                    SecurityTheme.dangerColor,
                    () => _navigateToScreen(const AlarmScreen()),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionCard(
                    'Rapporter',
                    Icons.note,
                    SecurityTheme.infoColor,
                    () => _navigateToScreen(const ReportScreen()),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Enhetsstatus',
                    Icons.devices,
                    SecurityTheme.successColor,
                    () => _navigateToScreen(const DeviceStatusScreen()),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionCard(
                    'Sök butiker',
                    Icons.search,
                    SecurityTheme.secondaryColor,
                    () => _navigateToScreen(const StoreSearchScreen()),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Aktiva vakter',
                    Icons.security,
                    SecurityTheme.primaryColor,
                    () => _navigateToScreen(const ActiveGuardsScreen()),
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(child: SizedBox()),
              ],
            ),

            const SizedBox(height: 24),

            // Senaste larm
            Text(
              'Senaste larm',
              style: TextStyle(
                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildRecentAlarms(),
          ],
        ),
      ),
    );
  }

  Widget _buildWarningCard(
    String title,
    String message,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: SecurityTheme.roundedBorder,
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(40),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: SecurityTheme.roundedBorder,
        child: InkWell(
          onTap: onTap,
          borderRadius: SecurityTheme.roundedBorder,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withAlpha(40),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        message,
                        style: TextStyle(
                          color: SecurityTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: color.withAlpha(30),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.arrow_forward, color: color, size: 18),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Container(
      height: 120, // Fast höjd för alla kort
      decoration: BoxDecoration(
        borderRadius: SecurityTheme.roundedBorder,
        boxShadow: SecurityTheme.cardShadow,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color,
            Color.lerp(color, Colors.black, 0.2) ?? color,
          ],
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: SecurityTheme.roundedBorder,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: ResponsiveLayout.getAdaptiveIconSize(context, 32),
                  color: Colors.white,
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 14),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecentAlarms() {
    final alarms = _alarmService.alarms;

    if (alarms.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: SecurityTheme.cardColor,
          borderRadius: SecurityTheme.roundedBorder,
          boxShadow: SecurityTheme.cardShadow,
        ),
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.notifications_off,
                size: 48,
                color: SecurityTheme.secondaryTextColor.withAlpha(150),
              ),
              const SizedBox(height: 16),
              Text(
                'Inga larm har registrerats än',
                style: TextStyle(
                  color: SecurityTheme.secondaryTextColor,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Visa de 5 senaste larmen
    final recentAlarms = alarms.take(5).toList();

    return Container(
      decoration: BoxDecoration(
        color: SecurityTheme.cardColor,
        borderRadius: SecurityTheme.roundedBorder,
        boxShadow: SecurityTheme.cardShadow,
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: recentAlarms.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final alarm = recentAlarms[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: alarm.type == AlarmType.red
                  ? SecurityTheme.dangerColor.withAlpha(30)
                  : SecurityTheme.warningColor.withAlpha(30),
              child: Icon(
                Icons.warning,
                color: alarm.type == AlarmType.red ? SecurityTheme.dangerColor : SecurityTheme.warningColor,
              ),
            ),
            title: Text(
              alarm.type == AlarmType.red ? 'AKUT LARM' : 'VARNINGSLARM',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: alarm.type == AlarmType.red ? SecurityTheme.dangerColor : SecurityTheme.warningColor,
              ),
            ),
            subtitle: Text(
              '${alarm.location} - ${_formatDateTime(alarm.timestamp)}',
              style: TextStyle(color: SecurityTheme.secondaryTextColor),
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: alarm.status == AlarmStatus.new_
                    ? SecurityTheme.dangerColor
                    : alarm.status == AlarmStatus.ongoing
                        ? SecurityTheme.infoColor
                        : SecurityTheme.successColor,
                borderRadius: BorderRadius.circular(SecurityTheme.borderRadius / 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(40),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                alarm.status == AlarmStatus.new_
                    ? 'NYTT'
                    : alarm.status == AlarmStatus.ongoing
                        ? 'PÅGÅENDE'
                        : 'ÅTGÄRDAT',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            onTap: () {
              Navigator.pushNamed(
                context,
                '/alarm_detail',
                arguments: alarm.id,
              );
            },
          );
        },
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
