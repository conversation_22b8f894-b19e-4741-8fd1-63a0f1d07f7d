<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT Webhook Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .button-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
        }
        
        .alert-button {
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .red-alert {
            background-color: #dc3545;
            color: white;
        }
        
        .red-alert:hover {
            background-color: #c82333;
            transform: translateY(-2px);
        }
        
        .yellow-alert {
            background-color: #ffc107;
            color: #212529;
        }
        
        .yellow-alert:hover {
            background-color: #e0a800;
            transform: translateY(-2px);
        }
        
        .alert-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            min-height: 20px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .config {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .config label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .config input {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 MQTT Webhook Tester</h1>
        
        <div class="config">
            <label for="webhookUrl">Webhook URL:</label>
            <input type="url" id="webhookUrl" value="https://mqttwebhook-jtc7nletgq-ey.a.run.app" placeholder="Enter webhook URL">
            
            <label for="buttonId">Button ID:</label>
            <input type="text" id="buttonId" value="test-button-123" placeholder="Enter button ID">
            
            <label for="username">Username:</label>
            <input type="text" id="username" value="test-user" placeholder="Enter username">
        </div>
        
        <div class="button-container">
            <button class="alert-button red-alert" onclick="sendAlert('red')">
                🔴 Send Red Alert
            </button>
            <button class="alert-button yellow-alert" onclick="sendAlert('yellow')">
                🟡 Send Yellow Alert
            </button>
        </div>
        
        <div id="feedback" class="feedback"></div>
    </div>

    <script>
        async function sendAlert(alertType) {
            const webhookUrl = document.getElementById('webhookUrl').value;
            const buttonId = document.getElementById('buttonId').value;
            const username = document.getElementById('username').value;
            const feedback = document.getElementById('feedback');
            const buttons = document.querySelectorAll('.alert-button');
            
            // Validate inputs
            if (!webhookUrl || !buttonId || !username) {
                showFeedback('Please fill in all fields', 'error');
                return;
            }
            
            // Disable buttons and show loading
            buttons.forEach(btn => btn.disabled = true);
            showFeedback('<span class="spinner"></span>Sending alert...', 'loading');
            
            // Prepare payload and topic based on alert type
            const payload = alertType === 'red' ? 'rod larm' : 'gul larm';
            const topic = alertType === 'red' ? '/store/Coop/button/red_alert' : '/store/Coop/button/gul_alert';

            const requestData = {
                clientid: buttonId,
                payload: payload,
                qos: 1,
                topic: topic,
                username: username
            };
            
            try {
                const response = await fetch(webhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const responseData = await response.json();
                
                if (response.ok) {
                    showFeedback(
                        `✅ ${alertType.toUpperCase()} alert sent successfully!<br>` +
                        `Document ID: ${responseData.id}<br>` +
                        `Status: ${responseData.status}`, 
                        'success'
                    );
                } else {
                    showFeedback(
                        `❌ Error: ${responseData.error || 'Unknown error'}<br>` +
                        `Status: ${response.status}`, 
                        'error'
                    );
                }
            } catch (error) {
                showFeedback(
                    `❌ Network error: ${error.message}<br>` +
                    `Please check the webhook URL and try again.`, 
                    'error'
                );
            } finally {
                // Re-enable buttons
                buttons.forEach(btn => btn.disabled = false);
            }
        }
        
        function showFeedback(message, type) {
            const feedback = document.getElementById('feedback');
            feedback.innerHTML = message;
            feedback.className = `feedback ${type}`;
        }
        
        // Test webhook connectivity on page load
        window.addEventListener('load', async () => {
            const webhookUrl = document.getElementById('webhookUrl').value;
            try {
                const response = await fetch(webhookUrl, { method: 'GET' });
                if (response.ok) {
                    const data = await response.json();
                    showFeedback(`✅ Webhook is online: ${data.message}`, 'success');
                } else {
                    showFeedback('⚠️ Webhook responded but with an error', 'error');
                }
            } catch (error) {
                showFeedback('⚠️ Could not connect to webhook', 'error');
            }
        });
    </script>
</body>
</html>
