rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions for role-based access control
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return request.auth.token.role == "admin";
    }

    function isGuard() {
      return request.auth.token.role == "guard";
    }

    function isStore() {
      return request.auth.token.role == "store";
    }

    function getUserStores() {
      return request.auth.token.stores;
    }

    function isSelf(userId) {
      return request.auth.uid == userId;
    }

    function hasStoreAccess(storeId) {
      return isAdmin() ||
             (isStore() && getUserStores().hasAny([storeId])) ||
             (isGuard() && getUserStores().hasAny([storeId]));
    }

    // users/: only admin can list all users
    match /users/{userId} {
      allow read: if isAdmin() || isSelf(userId);
      allow create: if isAdmin();
      allow update: if isAdmin() || (isSelf(userId) && !("role" in request.resource.data));
      allow delete: if isAdmin();
    }

    // stores/: only admin can write; store‑role can read their stores
    match /stores/{storeId} {
      allow create, update, delete: if isAdmin();
      allow read: if isAdmin() || hasStoreAccess(storeId);
    }

    // buttons/: admin full; store+guard read if button belongs to their store
    match /buttons/{buttonId} {
      allow create, update, delete: if isAdmin();
      allow read: if isAdmin() ||
        (resource.data.storeId != null && hasStoreAccess(resource.data.storeId));
    }

    // alerts/: guards see only alerts for their stores; stores see only theirs; admin sees all
    match /alerts/{alertId} {
      allow create: if isAuthenticated();  // Cloud functions or authenticated users
      allow read: if isAdmin() ||
         (resource.data.storeId != null && hasStoreAccess(resource.data.storeId));
      allow update: if isAdmin() ||
         (isGuard() && resource.data.storeId != null && hasStoreAccess(resource.data.storeId));

      // Allow access to reports subcollection
      match /reports/{reportId} {
        allow create: if isAuthenticated() &&
          (isAdmin() ||
          (get(/databases/$(database)/documents/alerts/$(alertId)).data.storeId != null &&
           hasStoreAccess(get(/databases/$(database)/documents/alerts/$(alertId)).data.storeId)));

        allow read: if isAuthenticated() &&
          (isAdmin() ||
          (get(/databases/$(database)/documents/alerts/$(alertId)).data.storeId != null &&
           hasStoreAccess(get(/databases/$(database)/documents/alerts/$(alertId)).data.storeId)));
      }
    }

    // requests/: store creates own; admin reads all; store reads own; admin updates status
    match /requests/{reqId} {
      allow create: if isStore() && request.resource.data.storeId != null &&
                     getUserStores().hasAny([request.resource.data.storeId]);
      allow read: if isAdmin() ||
                   (isStore() && resource.data.storeId != null &&
                    getUserStores().hasAny([resource.data.storeId]));
      allow update: if isAdmin();
    }

    // shifts/: admin creates; guards read only their shifts; admin reads all
    match /shifts/{shiftId} {
      allow create, update, delete: if isAdmin();
      allow read: if isAdmin() ||
                   (isGuard() && resource.data.guardId == request.auth.uid);
    }

    // reports/: store/guard create own; admin reads all; authors read own
    match /reports/{repId} {
      allow create: if (isStore() && request.resource.data.storeId != null &&
                        getUserStores().hasAny([request.resource.data.storeId])) ||
                     isGuard();
      allow read: if isAdmin() ||
                   resource.data.authorId == request.auth.uid ||
                   (resource.data.storeId != null && hasStoreAccess(resource.data.storeId));
    }

    // activities/: admin only
    match /activities/{actId} {
      allow read, write: if isAdmin();
    }

    // mqtt_messages/: admin read, cloud functions write
    match /mqtt_messages/{messageId} {
      allow read: if isAdmin();
      allow write: if false; // Only cloud functions can write
    }

    // notifications/: users can read their own
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow write: if false; // Only cloud functions can write
    }
  }
}
