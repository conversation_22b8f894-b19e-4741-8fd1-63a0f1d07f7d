# Firestore Database Structure

## Collections

### users
- Document ID: User's UID from Firebase Auth
- Fields:
  - username: String
  - name: String
  - role: Number (0 = guard, 1 = admin, 2 = customer)
  - email: String
  - phoneNumber: String
  - status: Number (0 = active, 1 = inactive, 2 = onLeave)
  - position: String
  - department: String
  - lastActive: Timestamp
  - handledAlarmIds: Array<String>
  - storeId: String (only for customers)

### stores
- Document ID: Auto-generated or custom ID
- Fields:
  - name: String
  - address: String
  - city: String
  - postalCode: String
  - contactPerson: String
  - phoneNumber: String
  - email: String
  - status: Number (0 = active, 1 = inactive, 2 = maintenance)
  - deviceIds: Array<String>
  - customerIds: Array<String>
  - notes: String
  - createdAt: Timestamp
  - updatedAt: Timestamp

### devices
- Document ID: Auto-generated or custom ID
- Fields:
  - name: String
  - storeId: String
  - batteryLevel: Number
  - lastActive: Timestamp
  - status: Number (0 = active, 1 = inactive, 2 = maintenance)
  - type: String
  - location: String
  - notes: String
  - createdAt: Timestamp
  - updatedAt: Timestamp

### alarms
- Document ID: Auto-generated or custom ID
- Fields:
  - storeId: String
  - deviceId: String
  - timestamp: Timestamp
  - status: Number (0 = active, 1 = acknowledged, 2 = resolved, 3 = false)
  - priority: Number (0 = low, 1 = medium, 2 = high)
  - description: String
  - handledBy: String (User ID)
  - handledAt: Timestamp
  - notes: String
  - attachmentUrls: Array<String>
  - createdAt: Timestamp
  - updatedAt: Timestamp

### guardOrders
- Document ID: Auto-generated or custom ID
- Fields:
  - storeId: String
  - storeName: String
  - createdAt: Timestamp
  - requestedDate: Timestamp
  - startTime: String
  - endTime: String
  - guardType: Number
  - numberOfGuards: Number
  - description: String
  - status: Number
  - adminFeedback: String
  - updatedAt: Timestamp
  - attachmentPaths: Array<String>
  - securityGuardPermitPath: String
  - statusHistory: Array<Map>
    - status: Number
    - timestamp: Timestamp
    - userId: String
    - notes: String
  - assignedUserId: String
  - assignedUserName: String

### reports
- Document ID: Auto-generated or custom ID
- Fields:
  - title: String
  - storeId: String
  - userId: String
  - userName: String
  - content: String
  - category: Number
  - status: Number
  - attachmentUrls: Array<String>
  - createdAt: Timestamp
  - updatedAt: Timestamp

### activityLogs
- Document ID: Auto-generated
- Fields:
  - userId: String
  - userName: String
  - action: String
  - details: String
  - timestamp: Timestamp
  - relatedEntityId: String
  - relatedEntityType: String

## Security Rules

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Authentication check
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Admin check
    function isAdmin() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 1;
    }
    
    // Guard check
    function isGuard() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 0;
    }
    
    // Customer check
    function isCustomer() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 2;
    }
    
    // Check if user is accessing their own data
    function isSelf(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Check if customer is accessing their own store
    function isOwnStore(storeId) {
      return isCustomer() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.storeId == storeId;
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isSelf(userId);
    }
    
    // Stores collection
    match /stores/{storeId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isOwnStore(storeId);
    }
    
    // Devices collection
    match /devices/{deviceId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Alarms collection
    match /alarms/{alarmId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAdmin() || isGuard();
    }
    
    // Guard orders collection
    match /guardOrders/{orderId} {
      allow read: if isAuthenticated();
      allow create: if isCustomer() || isAdmin();
      allow update, delete: if isAdmin();
    }
    
    // Reports collection
    match /reports/{reportId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAdmin() || isGuard();
    }
    
    // Activity logs collection
    match /activityLogs/{logId} {
      allow read: if isAdmin();
      allow create: if isAuthenticated();
      allow update, delete: if false; // No one can update or delete logs
    }
  }
}
```
