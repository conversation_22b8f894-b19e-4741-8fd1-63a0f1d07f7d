import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/mqtt_message.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';

class MqttMessageService {
  static final MqttMessageService _instance = MqttMessageService._internal();
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  
  // List to store MQTT messages
  final List<MqttMessage> _messages = [];
  
  // Stream controller for MQTT messages
  late StreamController<List<MqttMessage>> _messagesStreamController;
  
  // Stream subscription
  StreamSubscription? _mqttMessagesSubscription;
  
  factory MqttMessageService() {
    return _instance;
  }
  
  MqttMessageService._internal() {
    _messagesStreamController = StreamController<List<MqttMessage>>.broadcast();
    _setupMqttMessagesListener();
  }
  
  // Getter for messages
  List<MqttMessage> get messages => List.unmodifiable(_messages);
  
  // Getter for messages stream
  Stream<List<MqttMessage>> get messagesStream => _messagesStreamController.stream;
  
  // Setup listener for MQTT messages
  void _setupMqttMessagesListener() {
    // Only admin users can access MQTT messages
    if (!_authService.isAdmin) {
      return;
    }
    
    // Cancel existing subscription if any
    _mqttMessagesSubscription?.cancel();
    
    // Subscribe to MQTT messages
    _mqttMessagesSubscription = _databaseService.getMqttMessagesStream().listen((snapshot) {
      _messages.clear();
      
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final message = MqttMessage.fromMap(doc.id, data);
        _messages.add(message);
      }
      
      // Notify listeners
      if (!_messagesStreamController.isClosed) {
        _messagesStreamController.add(_messages);
      }
    });
  }
  
  // Get messages with red alert type
  List<MqttMessage> getRedAlerts() {
    return _messages.where((message) => message.alertType == AlertType.red).toList();
  }
  
  // Get messages with yellow alert type
  List<MqttMessage> getYellowAlerts() {
    return _messages.where((message) => message.alertType == AlertType.yellow).toList();
  }
  
  // Get messages for a specific store
  List<MqttMessage> getMessagesForStore(String storeName) {
    return _messages.where((message) => 
      message.topic.contains('/store/$storeName/') || 
      message.storeName == storeName
    ).toList();
  }
  
  // Refresh MQTT messages (for example when user logs in)
  void refresh() {
    _setupMqttMessagesListener();
  }
  
  // Dispose resources
  void dispose() {
    _mqttMessagesSubscription?.cancel();
    if (!_messagesStreamController.isClosed) {
      _messagesStreamController.close();
    }
  }
}
