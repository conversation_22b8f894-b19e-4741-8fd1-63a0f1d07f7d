const { onRequest } = require("firebase-functions/v2/https");
const { onDocumentCreated } = require("firebase-functions/v2/firestore");
const admin = require("firebase-admin");
const express = require("express");

// Import custom functions
const { setUserClaims } = require("./setUserClaims");

admin.initializeApp();

// Use the firestore instance from the admin SDK
const firestore = admin.firestore();

const app = express();

// Use express.json() middleware with proper error handling
app.use(express.json({
  limit: '10mb',
  verify: (req, res, buf, encoding) => {
    // Store raw body for debugging if needed
    req.rawBody = buf.toString('utf8');
  }
}));

// Error handling middleware for JSON parsing
app.use((error, req, res, next) => {
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    console.error("JSON parsing error:", error.message);
    console.error("Body that failed to parse:", req.rawBody);
    return res.status(400).send({
      error: "Invalid JSON format.",
      received_body: req.rawBody
    });
  }
  next();
});

// Health check endpoint
app.get("/", (req, res) => {
  res.status(200).send({
    status: "ok",
    timestamp: new Date().toISOString(),
    message: "MQTT Webhook is running"
  });
});

app.post("/", async (req, res) => {
  // Set a timeout for the entire request
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      console.error("Request timeout - sending 408");
      res.status(408).send({ error: "Request timeout" });
    }
  }, 55000); // 55 seconds, just under Cloud Functions timeout

  try {
    console.log(`MQTT Webhook received. Content-Type: ${req.headers["content-type"]}`);
    console.log("Raw body received:", req.rawBody);

    // Express.json() middleware already parsed the body
    const body = req.body || {};

    console.log("Parsed body:", JSON.stringify(body));

    // Check if the payload field exists and is not empty
    if (!body.payload || (typeof body.payload === 'object' && Object.keys(body.payload).length === 0)) {
      console.warn("WARNING: Empty or missing payload in MQTT webhook request");
    }

    // Create a document with only the requested fields
    const docData = {
      // Required fields as specified
      receivedAt: new Date().toISOString(),
      clientid: body.clientid || null,
      payload: body.payload || null,
      qos: body.qos || null,
      topic: body.topic || null,
      username: body.username || null
    };

    // Log detailed debug information but only save the required fields
    console.log("Saving document with data:", JSON.stringify(docData));

    // Add timeout to Firestore operation
    const docRef = await Promise.race([
      firestore.collection("mqtt_messages").add(docData),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Firestore operation timeout')), 30000)
      )
    ]);

    console.log("Document saved with ID:", docRef.id);

    // Clear timeout and send success response
    clearTimeout(timeout);
    if (!res.headersSent) {
      res.status(200).send({ status: "ok", id: docRef.id });
    }
  } catch (error) {
    console.error("MQTT Webhook error:", error);
    clearTimeout(timeout);
    if (!res.headersSent) {
      res.status(500).send({ error: "Internal Server Error", details: error.message });
    }
  }
});

exports.mqttWebhook = onRequest({
  region: 'europe-west3',
  timeoutSeconds: 60,
  memory: '256MiB',
  cors: true
}, app);

// Export the setUserClaims function
exports.setUserClaims = setUserClaims;

// Function to process MQTT messages and create alerts
exports.processMqttMessages = onDocumentCreated({
  document: 'mqtt_messages/{messageId}',
  region: 'europe-west3'
}, async (event) => {
  const snapshot = event.data;
  const context = { params: event.params };
  try {
    const messageData = snapshot.data();
    const messageId = context.params.messageId;

    console.log(`Processing MQTT message ${messageId}:`, JSON.stringify(messageData));

    // Check if this is a button press message
    if (!messageData.topic || !messageData.topic.includes('button')) {
      console.log(`Message ${messageId} is not a button press, skipping`);
      return null;
    }

    // Extract button ID from topic or clientid
    let buttonId = null;
    if (messageData.clientid) {
      buttonId = messageData.clientid;
    } else if (messageData.topic) {
      // Extract button ID from topic (format may vary)
      const topicParts = messageData.topic.split('/');
      if (topicParts.length > 1) {
        buttonId = topicParts[topicParts.length - 1];
      }
    }

    if (!buttonId) {
      console.log(`Could not determine button ID for message ${messageId}`);
      return null;
    }

    // Determine alert type from payload
    let alertType = 'active'; // Default type
    if (messageData.payload) {
      const payload = messageData.payload.toString().toLowerCase();
      if (payload.includes('rod larm') || payload.includes('röd larm')) {
        alertType = 'red';
        console.log(`Message ${messageId} contains red alert payload`);
      } else if (payload.includes('gul larm')) {
        alertType = 'yellow';
        console.log(`Message ${messageId} contains yellow alert payload`);
      }
    }

    // Look up the button in the database
    const buttonDoc = await admin.firestore().collection('buttons').doc(buttonId).get();

    if (!buttonDoc.exists) {
      console.log(`Button ${buttonId} not found in database`);

      // Create a new button if it doesn't exist
      await admin.firestore().collection('buttons').doc(buttonId).set({
        label: `New Button ${buttonId}`,
        storeId: 'unassigned',
        status: 'active',
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`Created new button ${buttonId}`);
      return null;
    }

    const buttonData = buttonDoc.data();

    // Skip if button is inactive
    if (buttonData.status === 'inactive' || buttonData.status === 'service') {
      console.log(`Button ${buttonId} is ${buttonData.status}, skipping alert creation`);
      return null;
    }

    // Create a new alert
    const alertData = {
      buttonId: buttonId,
      storeId: buttonData.storeId,
      pressedAt: admin.firestore.FieldValue.serverTimestamp(),
      status: 'active',
      alertType: alertType,
      mqttMessageId: messageId,
      payload: messageData.payload,
      topic: messageData.topic
    };

    const alertRef = await admin.firestore().collection('alerts').add(alertData);
    console.log(`Created new alert ${alertRef.id} for button ${buttonId} with type ${alertType}`);

    // Log activity
    await admin.firestore().collection('activities').add({
      action: 'alert_created',
      targetId: alertRef.id,
      buttonId: buttonId,
      storeId: buttonData.storeId,
      alertType: alertType,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      meta: {
        mqttMessageId: messageId,
        payload: messageData.payload
      }
    });

    return null;
    } catch (error) {
      console.error('Error processing MQTT message:', error);
      return null;
    }
  });

// Function to send notifications when a new alert is created
exports.sendAlertNotifications = onDocumentCreated({
  document: 'alerts/{alertId}',
  region: 'europe-west3'
}, async (event) => {
  const snapshot = event.data;
  const context = { params: event.params };
  try {
    const alertData = snapshot.data();
    const alertId = context.params.alertId;

    console.log(`Processing new alert ${alertId} for notifications`);

    // Get the store information
    const storeId = alertData.storeId;
    if (!storeId || storeId === 'unassigned') {
      console.log(`Alert ${alertId} has no valid storeId, skipping notifications`);
      return null;
    }

    const storeDoc = await admin.firestore().collection('stores').doc(storeId).get();
    if (!storeDoc.exists) {
      console.log(`Store ${storeId} not found for alert ${alertId}`);
      return null;
    }

    const storeData = storeDoc.data();

    // Find all users who should be notified
    // 1. All admins
    // 2. Guards assigned to this store
    // 3. Store users for this store

    // Get all users
    const usersSnapshot = await admin.firestore().collection('users').get();

    // Filter users who should be notified
    const usersToNotify = [];

    usersSnapshot.forEach(userDoc => {
      const userData = userDoc.data();

      // Check if user should be notified based on role and store assignment
      if (userData.role === 1) { // Admin
        usersToNotify.push({
          userId: userDoc.id,
          ...userData
        });
      } else if (userData.role === 0 && userData.stores && userData.stores.includes(storeId)) { // Guard assigned to this store
        usersToNotify.push({
          userId: userDoc.id,
          ...userData
        });
      } else if (userData.role === 2 && userData.storeId === storeId) { // Store user for this store
        usersToNotify.push({
          userId: userDoc.id,
          ...userData
        });
      }
    });

    console.log(`Found ${usersToNotify.length} users to notify for alert ${alertId}`);

    // For each user, create a notification in the database
    // In a real app, you would also send push notifications here
    for (const user of usersToNotify) {
      await admin.firestore().collection('notifications').add({
        userId: user.userId,
        alertId: alertId,
        storeId: storeId,
        storeName: storeData.name || 'Unknown Store',
        title: 'New Alert',
        message: `Alert from ${storeData.name || 'Unknown Store'}`,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        read: false
      });

      console.log(`Created notification for user ${user.userId} for alert ${alertId}`);
    }

    return null;
    } catch (error) {
      console.error('Error sending alert notifications:', error);
      return null;
    }
  });

// Simple Alert Sender Function (bypasses MQTT, directly creates alerts)
exports.sendMqttAlert = onRequest({
  region: 'europe-west3',
  timeoutSeconds: 30,
  memory: '256MiB',
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { alertType } = req.body;

    // Validate alert type
    if (!alertType || !['red', 'yellow'].includes(alertType)) {
      return res.status(400).json({
        error: 'Invalid alert type. Must be "red" or "yellow"'
      });
    }

    // Simulate MQTT message by directly creating the alert in Firestore
    // This bypasses the MQTT broker but achieves the same result
    const topic = alertType === 'red' ? '/store/Coop/button/red_alert' : '/store/Coop/button/yellow_alert';
    const payload = alertType === 'red' ? 'rod larm' : 'gul larm';

    // Create a simulated MQTT message document
    const mqttMessageData = {
      receivedAt: new Date().toISOString(),
      clientid: 'web-test-client',
      payload: payload,
      qos: 1,
      topic: topic,
      username: 'test'
    };

    // Add the MQTT message to Firestore (this will trigger the existing processMqttMessages function)
    const docRef = await admin.firestore().collection("mqtt_messages").add(mqttMessageData);

    console.log(`Created simulated MQTT message ${docRef.id} for ${alertType} alert`);

    res.status(200).json({
      success: true,
      alertType,
      topic,
      payload,
      timestamp: new Date().toISOString(),
      mqttMessageId: docRef.id,
      message: 'Alert sent successfully via simulated MQTT message'
    });

  } catch (error) {
    console.error('Error in sendMqttAlert function:', error);
    res.status(500).json({
      error: 'Failed to send alert',
      details: error.message
    });
  }
});