import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

// Mock class to replace FilePickerResult
class MockFilePickerResult {
  final List<MockPlatformFile> files;
  final List<String?> paths;

  MockFilePickerResult({
    required this.files,
    required this.paths,
  });
}

// Mock class to replace PlatformFile
class MockPlatformFile {
  final String name;
  final String? path;

  MockPlatformFile({
    required this.name,
    this.path,
  });
}

class FileService {
  static final FileService _instance = FileService._internal();

  factory FileService() {
    return _instance;
  }

  FileService._internal();

  // Mock implementation for file picking
  Future<MockFilePickerResult?> pickFile({
    dynamic type = 'any',
    List<String>? allowedExtensions,
  }) async {
    // Return a mock file result
    final mockFile = MockPlatformFile(
      name: 'mock_document.pdf',
      path: '/mock/path/mock_document.pdf',
    );

    return MockFilePickerResult(
      files: [mockFile],
      paths: [mockFile.path],
    );
  }

  // Mock implementation for multiple file picking
  Future<MockFilePickerResult?> pickMultipleFiles({
    dynamic type = 'any',
    List<String>? allowedExtensions,
  }) async {
    // Return mock files
    final mockFiles = [
      MockPlatformFile(
        name: 'mock_document1.pdf',
        path: '/mock/path/mock_document1.pdf',
      ),
      MockPlatformFile(
        name: 'mock_image.jpg',
        path: '/mock/path/mock_image.jpg',
      ),
    ];

    return MockFilePickerResult(
      files: mockFiles,
      paths: mockFiles.map((file) => file.path).toList(),
    );
  }

  // Mock implementation for saving a file
  Future<String?> saveFile(File file) async {
    // Return a mock file path
    return '/mock/saved/${const Uuid().v4()}.pdf';
  }

  // Mock implementation for saving multiple files
  Future<List<String>> saveFiles(List<File> files) async {
    // Return mock file paths
    return List.generate(
      files.length,
      (index) => '/mock/saved/${const Uuid().v4()}.${index % 2 == 0 ? 'pdf' : 'jpg'}'
    );
  }

  // Mock implementation for getting a file
  Future<File?> getFile(String filePath) async {
    // Always return null in mock implementation
    return null;
  }

  // Mock implementation for deleting a file
  Future<bool> deleteFile(String filePath) async {
    // Always return true in mock implementation
    return true;
  }

  // Get filename from path
  String getFileName(String filePath) {
    return path.basename(filePath);
  }

  // Check if a file is a PDF
  bool isPdf(String filePath) {
    return path.extension(filePath).toLowerCase() == '.pdf';
  }

  // Check if a file is an image
  bool isImage(String filePath) {
    final ext = path.extension(filePath).toLowerCase();
    return ext == '.jpg' || ext == '.jpeg' || ext == '.png' || ext == '.gif';
  }
}
