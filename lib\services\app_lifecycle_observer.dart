import 'dart:async';
import 'package:flutter/material.dart';
import 'global_alert_listener.dart';

/// A class that observes app lifecycle changes and manages services accordingly
class AppLifecycleObserver with WidgetsBindingObserver {
  final GlobalAlertListener _alertListener = GlobalAlertListener();

  AppLifecycleObserver() {
    // Register this observer with the binding
    WidgetsBinding.instance.addObserver(this);

    // Start the alert listener immediately
    _startAlertListener();

    // Also schedule another start after a short delay to ensure it's running
    // This helps in cases where the app might not be fully initialized yet
    Future.delayed(const Duration(seconds: 2), () {
      _startAlertListener();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    debugPrint('App lifecycle state changed to: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        // App is visible and running in the foreground
        _startAlertListener();
        break;
      case AppLifecycleState.inactive:
        // App is in an inactive state (e.g., in a phone call)
        // Keep the listener active to ensure we don't miss alerts
        break;
      case AppLifecycleState.paused:
        // App is not visible but still running in the background
        // Keep the listener active to ensure we don't miss alerts
        break;
      case AppLifecycleState.detached:
        // App is detached from the UI (e.g., process killed)
        // Stop the listener to free resources
        _alertListener.stopListening();
        break;
      case AppLifecycleState.hidden:
        // App is hidden but still running
        // Keep the listener active to ensure we don't miss alerts
        break;
    }
  }

  void _startAlertListener() {
    // First stop any existing listeners to avoid duplicates
    _alertListener.stopListening();

    // Start the listener with a short delay to ensure everything is initialized
    Future.delayed(const Duration(milliseconds: 300), () {
      debugPrint('Starting alert listener from lifecycle observer');
      _alertListener.startListening();

      // Force a refresh of the listener after a short delay
      // This ensures we catch any alerts that might have been missed
      Future.delayed(const Duration(seconds: 1), () {
        debugPrint('Refreshing alert listener to ensure it\'s active');
        // This is a no-op if already listening, but helps ensure the listener is active
        _alertListener.startListening();

        // Set up a periodic check to ensure the listener is always running
        _setupPeriodicListenerCheck();
      });
    });
  }

  // Timer for periodic checks
  Timer? _periodicCheckTimer;

  /// Set up a periodic check to ensure the alert listener is always running
  void _setupPeriodicListenerCheck() {
    // Cancel any existing timer
    _periodicCheckTimer?.cancel();

    // Create a new timer that checks every 30 seconds
    _periodicCheckTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      debugPrint('Performing periodic check of alert listener');

      // Restart the listener to ensure it's running
      // This is a no-op if already listening
      _alertListener.startListening();
    });
  }

  void dispose() {
    // Unregister this observer when it's no longer needed
    WidgetsBinding.instance.removeObserver(this);

    // Cancel the periodic check timer
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = null;

    // Stop the alert listener
    _alertListener.stopListening();
  }
}
