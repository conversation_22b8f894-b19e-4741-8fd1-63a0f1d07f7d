import 'package:flutter/material.dart';
import '../../models/store.dart';
import '../../models/iot_device.dart';
import '../../services/store_service.dart';
import '../../widgets/home_button.dart';

class StoreSearchScreen extends StatefulWidget {
  const StoreSearchScreen({super.key});

  @override
  State<StoreSearchScreen> createState() => _StoreSearchScreenState();
}

class _StoreSearchScreenState extends State<StoreSearchScreen> {
  final StoreService _storeService = StoreService();
  final TextEditingController _searchController = TextEditingController();

  List<Store> _stores = [];
  List<Store> _filteredStores = [];

  @override
  void initState() {
    super.initState();
    _loadStores();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadStores() {
    _stores = _storeService.getAllStores();
    _filteredStores = List.from(_stores);
    setState(() {});
  }

  void _filterStores(String query) {
    if (query.isEmpty) {
      _filteredStores = List.from(_stores);
    } else {
      _filteredStores = _storeService.searchStores(query);
    }
    setState(() {});
  }

  void _viewStoreDetails(Store store) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildStoreDetailsSheet(store),
    );
  }

  Widget _buildStoreDetailsSheet(Store store) {
    final devices = _storeService.getDevicesForStore(store.id);

    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.3,
      maxChildSize: 0.9,
      expand: false,
      builder: (context, scrollController) {
        return SingleChildScrollView(
          controller: scrollController,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                store.name,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                store.fullAddress,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 16),
              const Divider(),

              // Kontaktinformation
              const Text(
                'Kontaktinformation',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              if (store.contactPerson.isNotEmpty)
                _buildInfoRow(Icons.person, 'Kontaktperson', store.contactPerson),
              if (store.contactPhone.isNotEmpty)
                _buildInfoRow(Icons.phone, 'Telefon', store.contactPhone),
              if (store.contactEmail.isNotEmpty)
                _buildInfoRow(Icons.email, 'E-post', store.contactEmail),

              const SizedBox(height: 16),
              const Divider(),

              // Enheter
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Enheter',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${devices.length} st',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              if (devices.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: Text('Inga enheter registrerade'),
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: devices.length,
                  itemBuilder: (context, index) {
                    final device = devices[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: Icon(
                          Icons.device_hub,
                          color: device.isActive
                              ? Colors.green
                              : Colors.grey,
                        ),
                        title: Text(device.name),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('ID: ${device.id}'),
                            Row(
                              children: [
                                Icon(
                                  Icons.battery_full,
                                  size: 16,
                                  color: device.isLowBattery
                                      ? Colors.red
                                      : Colors.green,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Batteri: ${device.batteryLevel}%',
                                  style: TextStyle(
                                    color: device.isLowBattery
                                        ? Colors.red
                                        : null,
                                    fontWeight: device.isLowBattery
                                        ? FontWeight.bold
                                        : null,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        trailing: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: device.status == DeviceStatus.active
                                ? Colors.green
                                : device.status == DeviceStatus.maintenance
                                    ? Colors.orange
                                    : Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            device.status == DeviceStatus.active
                                ? 'Aktiv'
                                : device.status == DeviceStatus.maintenance
                                    ? 'Underhåll'
                                    : 'Inaktiv',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.blue),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sök butiker'),
        actions: const [
          HomeButton(),
        ],
      ),
      body: Column(
        children: [
          // Sökfält
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Sök butiker',
                hintText: 'Ange namn, adress eller stad...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterStores('');
                        },
                      )
                    : null,
              ),
              onChanged: _filterStores,
            ),
          ),

          // Butikslista
          Expanded(
            child: _filteredStores.isEmpty
                ? const Center(
                    child: Text(
                      'Inga butiker hittades',
                      style: TextStyle(fontSize: 16),
                    ),
                  )
                : ListView.builder(
                    itemCount: _filteredStores.length,
                    itemBuilder: (context, index) {
                      final store = _filteredStores[index];
                      final devices = _storeService.getDevicesForStore(store.id);
                      final activeDevices = devices.where((d) => d.isActive).length;

                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          title: Text(
                            store.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(store.fullAddress),
                              Row(
                                children: [
                                  Icon(
                                    Icons.device_hub,
                                    size: 16,
                                    color: activeDevices > 0 ? Colors.green : Colors.grey,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Enheter: $activeDevices / ${devices.length} aktiva',
                                    style: TextStyle(
                                      color: activeDevices == 0 && devices.isNotEmpty
                                          ? Colors.red
                                          : null,
                                      fontWeight: activeDevices == 0 && devices.isNotEmpty
                                          ? FontWeight.bold
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () => _viewStoreDetails(store),
                          isThreeLine: true,
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
