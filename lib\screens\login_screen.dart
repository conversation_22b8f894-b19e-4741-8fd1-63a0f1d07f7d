import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../theme/security_theme.dart';
import '../widgets/security_logo.dart';

class LoginScreen extends StatefulWidget {
  final Function(bool) onLoginSuccess;

  const LoginScreen({super.key, required this.onLoginSuccess});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  final AuthService _authService = AuthService();

  bool _isLoading = false;
  String? _errorMessage;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = await _authService.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (user != null) {
        // Anropa callback för att uppdatera förälderns tillstånd
        widget.onLoginSuccess(user.isAdmin);

        // Navigera till rätt dashboard baserat på användartyp
        if (mounted) {
          if (user.isAdmin) {
            Navigator.of(context).pushNamedAndRemoveUntil('/admin_dashboard', (route) => false);
          } else if (user.isGuard) {
            Navigator.of(context).pushNamedAndRemoveUntil('/guard_dashboard', (route) => false);
          } else {
            Navigator.of(context).pushNamedAndRemoveUntil('/customer_dashboard', (route) => false);
          }
        }
      } else {
        setState(() {
          _errorMessage = 'Felaktigt användarnamn eller lösenord';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Ett fel uppstod: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              SecurityTheme.backgroundColor,
              Color.lerp(SecurityTheme.backgroundColor, SecurityTheme.primaryColor, 0.1) ??
                  SecurityTheme.backgroundColor,
            ],
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Logo och titel
                  const SecurityLogo(
                    size: 100,
                    isVertical: true,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Logga in för att fortsätta',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Felmeddelande
                  if (_errorMessage != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: SecurityTheme.dangerColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(SecurityTheme.borderRadius),
                        border: Border.all(
                          color: SecurityTheme.dangerColor.withAlpha(75),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: SecurityTheme.dangerColor,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(color: SecurityTheme.dangerColor),
                              textAlign: TextAlign.left,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Email
                  TextFormField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: 'E-post',
                      prefixIcon: Icon(Icons.email),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Ange din e-postadress';
                      }
                      if (!value.contains('@') || !value.contains('.')) {
                        return 'Ange en giltig e-postadress';
                      }
                      return null;
                    },
                    enabled: !_isLoading,
                    textInputAction: TextInputAction.next,
                  ),
                  const SizedBox(height: 16),

                  // Lösenord
                  TextFormField(
                    controller: _passwordController,
                    decoration: InputDecoration(
                      labelText: 'Lösenord',
                      prefixIcon: const Icon(Icons.lock),
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                    ),
                    obscureText: _obscurePassword,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Ange ditt lösenord';
                      }
                      return null;
                    },
                    enabled: !_isLoading,
                    onFieldSubmitted: (_) => _login(),
                  ),
                  const SizedBox(height: 24),

                  // Inloggningsknapp
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(SecurityTheme.borderRadius),
                      boxShadow: SecurityTheme.buttonShadow,
                    ),
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _login,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 3,
                              ),
                            )
                          : const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.lock_open),
                                SizedBox(width: 8),
                                Text('LOGGA IN'),
                              ],
                            ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Hjälptext
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: SecurityTheme.infoColor.withAlpha(15),
                      borderRadius: BorderRadius.circular(SecurityTheme.borderRadius),
                      border: Border.all(
                        color: SecurityTheme.infoColor.withAlpha(50),
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: SecurityTheme.infoColor,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Demo-inloggningar',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: SecurityTheme.infoColor,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Använd "<EMAIL>" / "admin123" för administratör\n'
                          'eller "<EMAIL>" / "vakt123" för vakt\n'
                          'eller "<EMAIL>" / "butik123" för butik',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
