<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT Alert Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 700px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .note {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin-bottom: 20px;
        }
        
        .button-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
        }
        
        .alert-button {
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .red-alert {
            background-color: #dc3545;
            color: white;
        }
        
        .red-alert:hover:not(:disabled) {
            background-color: #c82333;
            transform: translateY(-2px);
        }
        
        .yellow-alert {
            background-color: #ffc107;
            color: #212529;
        }
        
        .yellow-alert:hover:not(:disabled) {
            background-color: #e0a800;
            transform: translateY(-2px);
        }
        
        .alert-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            min-height: 20px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .security-info {
            background-color: #e7f3ff;
            color: #004085;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #b3d7ff;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Secure MQTT Alert Tester</h1>
        
        <div class="security-info">
            <strong>🔐 Security:</strong> This tool uses a secure backend API to send MQTT alerts. 
            Your broker credentials are safely stored on the server and never exposed to the browser.
        </div>
        
        <div class="note">
            <strong>Note:</strong> Click the buttons below to send test alerts through the secure backend. 
            The alerts will be sent to your MQTT broker and processed by your webhook system.
        </div>
        
        <div class="button-container">
            <button class="alert-button red-alert" id="redAlertBtn" onclick="sendAlert('red')">
                🔴 Send Red Alert
            </button>
            <button class="alert-button yellow-alert" id="yellowAlertBtn" onclick="sendAlert('yellow')">
                🟡 Send Yellow Alert
            </button>
        </div>
        
        <div id="feedback" class="feedback"></div>
    </div>

    <script>
        // Configuration - Firebase Function URL
        const API_BASE_URL = 'https://sendmqttalert-jtc7nletgq-ey.a.run.app';
        
        async function sendAlert(alertType) {
            const button = document.getElementById(alertType === 'red' ? 'redAlertBtn' : 'yellowAlertBtn');
            const allButtons = document.querySelectorAll('.alert-button');
            
            // Disable all buttons during request
            allButtons.forEach(btn => btn.disabled = true);
            
            showFeedback('<span class="spinner"></span>Sending alert...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        alertType: alertType
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showFeedback(
                        `✅ ${alertType.toUpperCase()} alert sent successfully!<br>` +
                        `Topic: ${result.topic}<br>` +
                        `Payload: ${result.payload}<br>` +
                        `Time: ${new Date(result.timestamp).toLocaleString()}`, 
                        'success'
                    );
                } else {
                    throw new Error(result.error || 'Unknown error occurred');
                }
                
            } catch (error) {
                console.error('Error sending alert:', error);
                showFeedback(`❌ Failed to send alert: ${error.message}`, 'error');
            } finally {
                // Re-enable all buttons
                allButtons.forEach(btn => btn.disabled = false);
            }
        }
        
        function showFeedback(message, type) {
            const feedback = document.getElementById('feedback');
            feedback.innerHTML = message;
            feedback.className = `feedback ${type}`;
        }
        
        // Show ready message on page load
        window.addEventListener('load', () => {
            showFeedback('✅ Ready to send secure MQTT alerts!', 'success');
        });
    </script>
</body>
</html>
