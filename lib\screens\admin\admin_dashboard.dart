import 'dart:async';
import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../models/guard_order.dart';
import '../../models/alarm.dart';
import '../../models/activity_log.dart';
import '../../services/auth_service.dart';
import '../../services/store_service.dart';
import '../../services/alarm_service.dart';
import '../../utils/responsive_layout.dart';
import '../../theme/security_theme.dart';
import '../../widgets/security_logo.dart';
import 'store_management_screen.dart';
import 'device_management_screen.dart';
import 'user_management_screen.dart';
import '../guard/active_guards_screen.dart';
import 'manage_guard_orders_screen.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  final AuthService _authService = AuthService();
  final StoreService _storeService = StoreService();
  final AlarmService _alarmService = AlarmService();

  User? _currentUser;
  int _storeCount = 0;
  int _deviceCount = 0;
  int _lowBatteryCount = 0;
  int _pendingOrdersCount = 0;
  int _approvedOrdersCount = 0;
  int _completedOrdersCount = 0;
  int _cancelledOrdersCount = 0;
  int _totalOrdersCount = 0;

  // Larmstatistik
  int _newAlarmsCount = 0;
  int _ongoingAlarmsCount = 0;
  int _resolvedAlarmsCount = 0;
  int _totalAlarmsCount = 0;

  // Aktivitetsstatistik
  int _guardActionsCount = 0;
  int _adminActionsCount = 0;
  int _storeActionsCount = 0;

  // Senaste aktiviteter
  List<ActivityLog> _recentActivities = [];

  @override
  void initState() {
    super.initState();

    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      _currentUser = currentUser;
      _loadData();
    } else {
      // Hantera fallet när användaren inte är inloggad
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _authService.logout(); // Säkerställ att användaren loggas ut
      });
    }
  }

  void _loadData() {
    final stores = _storeService.getAllStores();
    final devices = _storeService.getAllDevices();
    final orders = _storeService.getAllGuardOrders();
    final alarms = _alarmService.getAllAlarms();

    // Real activities will be fetched from Firestore
    final List<ActivityLog> activities = [];

    setState(() {
      // Butiksstatistik
      _storeCount = stores.length;
      _deviceCount = devices.length;
      _lowBatteryCount = devices.where((d) => d.isLowBattery).length;

      // Beställningsstatistik
      _pendingOrdersCount = orders.where((o) => o.status == OrderStatus.pending).length;
      _approvedOrdersCount = orders.where((o) => o.status == OrderStatus.approved).length;
      _completedOrdersCount = orders.where((o) => o.status == OrderStatus.completed).length;
      _cancelledOrdersCount = orders.where((o) => o.status == OrderStatus.cancelled).length;
      _totalOrdersCount = orders.length;

      // Larmstatistik
      _newAlarmsCount = alarms.where((a) => a.status == AlarmStatus.new_).length;
      _ongoingAlarmsCount = alarms.where((a) => a.status == AlarmStatus.ongoing).length;
      _resolvedAlarmsCount = alarms.where((a) => a.status == AlarmStatus.resolved).length;
      _totalAlarmsCount = alarms.length;

      // Aktivitetsstatistik
      _guardActionsCount = activities.where((a) => a.source == ActivitySource.guard).length;
      _adminActionsCount = activities.where((a) => a.source == ActivitySource.admin).length;
      _storeActionsCount = activities.where((a) => a.source == ActivitySource.store).length;

      // Senaste aktiviteter
      _recentActivities = activities;
    });
  }

  // Activities will be fetched from Firestore

  void _logout() {
    showDialog(
      context: context,
      barrierDismissible: false, // Förhindra att användaren stänger dialogen genom att klicka utanför
      builder: (context) => AlertDialog(
        title: const Text('Logga ut'),
        content: const Text('Är du säker på att du vill logga ut?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Avbryt'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Visa laddningsindikator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );

              // Stäng dialogerna
              Navigator.pop(context); // Stäng laddningsindikatorn
              Navigator.pop(context); // Stäng bekräftelsedialogen

              // Logga ut användaren
              await _authService.logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logga ut'),
          ),
        ],
      ),
    );
  }

  void _navigateToStoreManagement() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StoreManagementScreen(),
      ),
    ).then((_) => _loadData());
  }

  void _navigateToDeviceManagement() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DeviceManagementScreen(),
      ),
    ).then((_) => _loadData());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const SecurityLogo(
              size: 36,
              showText: false,
            ),
            const SizedBox(width: 12),
            const Text('Administratörspanel'),
          ],
        ),
        actions: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.bar_chart),
              onPressed: () {
                Scaffold.of(context).openEndDrawer();
              },
              tooltip: 'Visa statistik',
            ),
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Logga ut',
          ),
        ],
        elevation: 2,
        shadowColor: Colors.black.withAlpha(50),
      ),
      endDrawer: _buildStatisticsDrawer(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Välkomstsektion
            Container(
              decoration: BoxDecoration(
                gradient: SecurityTheme.primaryGradient,
                borderRadius: SecurityTheme.roundedBorder,
                boxShadow: SecurityTheme.cardShadow,
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(50),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.admin_panel_settings,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Välkommen, ${_currentUser?.name ?? "Administratör"}',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            'Administratör',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white.withAlpha(200),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Snabbåtkomst
            const Text(
              'Snabbåtkomst',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Hantera butiker',
                    Icons.store,
                    SecurityTheme.primaryColor,
                    _navigateToStoreManagement,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionCard(
                    'Hantera enheter',
                    Icons.devices,
                    SecurityTheme.successColor,
                    _navigateToDeviceManagement,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Hantera personal',
                    Icons.people,
                    SecurityTheme.secondaryColor,
                    () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const UserManagementScreen(),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionCard(
                    'Aktiva vakter',
                    Icons.security,
                    SecurityTheme.infoColor,
                    () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ActiveGuardsScreen(),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Visa larm',
                    Icons.warning,
                    SecurityTheme.warningColor,
                    () {
                      Navigator.pushNamed(context, '/alarms');
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionCard(
                    'Visa statistik',
                    Icons.bar_chart,
                    SecurityTheme.secondaryColor,
                    () {
                      Navigator.pushNamed(context, '/statistics');
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Hantera vaktbeställningar',
                    Icons.security,
                    SecurityTheme.accentColor,
                    () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ManageGuardOrdersScreen(),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Builder(
                    builder: (context) => _buildActionCard(
                      'Översikt',
                      Icons.dashboard,
                      SecurityTheme.primaryColor,
                      () {
                        // Öppna statistik-drawer
                        Scaffold.of(context).openEndDrawer();
                      },
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Senaste aktivitet
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Senaste aktivitet',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: SecurityTheme.primaryColor.withAlpha(20),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  child: Row(
                    children: [
                      Icon(
                        Icons.people,
                        size: 16,
                        color: SecurityTheme.primaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Alla aktiviteter',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: SecurityTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: SecurityTheme.cardColor,
                borderRadius: SecurityTheme.roundedBorder,
                boxShadow: SecurityTheme.cardShadow,
              ),
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _recentActivities.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final activity = _recentActivities[index];
                  final timeString = _formatTime(activity.timestamp);

                  return ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: activity.getColor().withAlpha(30),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        activity.icon,
                        color: activity.getColor(),
                        size: 20,
                      ),
                    ),
                    title: Row(
                      children: [
                        Text(
                          activity.userName ?? 'System',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getSourceColor(activity.source).withAlpha(30),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            activity.getSourceText(),
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: _getSourceColor(activity.source),
                            ),
                          ),
                        ),
                      ],
                    ),
                    subtitle: Text(
                      activity.getDescription(),
                      style: TextStyle(color: SecurityTheme.secondaryTextColor),
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          timeString,
                          style: TextStyle(
                            color: SecurityTheme.secondaryTextColor,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _formatDate(activity.timestamp),
                          style: TextStyle(
                            color: SecurityTheme.secondaryTextColor.withAlpha(150),
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Container(
      height: 120, // Fast höjd för alla kort
      decoration: BoxDecoration(
        borderRadius: SecurityTheme.roundedBorder,
        boxShadow: SecurityTheme.cardShadow,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color,
            Color.lerp(color, Colors.black, 0.2) ?? color,
          ],
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: SecurityTheme.roundedBorder,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: Colors.white,
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsDrawer() {
    return Drawer(
      width: MediaQuery.of(context).size.width * 0.85,
      child: Container(
        decoration: BoxDecoration(
          color: SecurityTheme.backgroundColor,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Drawer header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: SecurityTheme.primaryGradient,
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.cardShadow,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.bar_chart,
                              color: Colors.white,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Statistik',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.white),
                          onPressed: () => Navigator.pop(context),
                          tooltip: 'Stäng',
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Knapp för detaljerad statistik
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.buttonShadow,
                    ),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context); // Stäng drawer först
                        Navigator.pushNamed(context, '/statistics').then((_) => _loadData());
                      },
                      icon: const Icon(Icons.analytics),
                      label: const Text('Visa detaljerad statistik'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Aktivitetsstatistik
                  Text(
                    'Aktivitetsstatistik',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: SecurityTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Aktivitetsstatistik kort
                  Container(
                    decoration: BoxDecoration(
                      color: SecurityTheme.cardColor,
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.cardShadow,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          _buildStatRow(
                            'Admin-aktiviteter',
                            _adminActionsCount.toString(),
                            Icons.admin_panel_settings,
                            SecurityTheme.primaryColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Vakt-aktiviteter',
                            _guardActionsCount.toString(),
                            Icons.security,
                            SecurityTheme.successColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Butiks-aktiviteter',
                            _storeActionsCount.toString(),
                            Icons.store,
                            SecurityTheme.warningColor,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Larmstatistik
                  Text(
                    'Larmstatistik',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: SecurityTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Larmstatistik kort
                  Container(
                    decoration: BoxDecoration(
                      color: SecurityTheme.cardColor,
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.cardShadow,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          _buildStatRow(
                            'Nya larm',
                            _newAlarmsCount.toString(),
                            Icons.notification_important,
                            SecurityTheme.dangerColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Pågående larm',
                            _ongoingAlarmsCount.toString(),
                            Icons.warning,
                            SecurityTheme.warningColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Åtgärdade larm',
                            _resolvedAlarmsCount.toString(),
                            Icons.check_circle,
                            SecurityTheme.successColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Totalt antal larm',
                            _totalAlarmsCount.toString(),
                            Icons.analytics,
                            SecurityTheme.infoColor,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Butiksstatistik
                  Text(
                    'Butiksstatistik',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: SecurityTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Butiksstatistik kort
                  Container(
                    decoration: BoxDecoration(
                      color: SecurityTheme.cardColor,
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.cardShadow,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          _buildStatRow(
                            'Butiker',
                            _storeCount.toString(),
                            Icons.store,
                            SecurityTheme.primaryColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Enheter',
                            _deviceCount.toString(),
                            Icons.devices,
                            SecurityTheme.successColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Lågt batteri',
                            _lowBatteryCount.toString(),
                            Icons.battery_alert,
                            SecurityTheme.dangerColor,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Beställningsstatistik
                  Text(
                    'Beställningsstatistik',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: SecurityTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Beställningsstatistik kort
                  Container(
                    decoration: BoxDecoration(
                      color: SecurityTheme.cardColor,
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.cardShadow,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          _buildStatRow(
                            'Väntande',
                            _pendingOrdersCount.toString(),
                            Icons.pending_actions,
                            SecurityTheme.warningColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Godkända',
                            _approvedOrdersCount.toString(),
                            Icons.check_circle,
                            SecurityTheme.successColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Genomförda',
                            _completedOrdersCount.toString(),
                            Icons.task_alt,
                            SecurityTheme.infoColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Avbrutna',
                            _cancelledOrdersCount.toString(),
                            Icons.cancel,
                            SecurityTheme.dangerColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Totalt antal',
                            _totalOrdersCount.toString(),
                            Icons.analytics,
                            SecurityTheme.secondaryColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String title, String value, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: color.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: SecurityTheme.primaryTextColor,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: color.withAlpha(20),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Hjälpmetod för att formatera tid (HH:MM)
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  // Hjälpmetod för att formatera datum (YYYY-MM-DD)
  String _formatDate(DateTime dateTime) {
    final year = dateTime.year;
    final month = dateTime.month.toString().padLeft(2, '0');
    final day = dateTime.day.toString().padLeft(2, '0');
    return '$year-$month-$day';
  }

  // Hjälpmetod för att få färg baserat på källa
  Color _getSourceColor(ActivitySource source) {
    switch (source) {
      case ActivitySource.admin:
        return SecurityTheme.primaryColor;
      case ActivitySource.guard:
        return SecurityTheme.successColor;
      case ActivitySource.store:
        return SecurityTheme.warningColor;
      case ActivitySource.system:
        return SecurityTheme.secondaryTextColor;
    }
  }
}
