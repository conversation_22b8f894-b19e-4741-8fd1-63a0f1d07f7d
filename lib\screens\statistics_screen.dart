import 'package:flutter/material.dart';
import '../models/alarm.dart';
import '../services/alarm_service.dart';
import '../widgets/home_button.dart';
import 'alarm_detail_screen.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  final AlarmService _alarmService = AlarmService();

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _viewAlarmDetails(String alarmId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AlarmDetailScreen(alarmId: alarmId),
      ),
    ).then((_) {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final alarms = _alarmService.alarms;

    if (alarms.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Statistik'),
          actions: const [
            HomeButton(),
          ],
        ),
        body: const Center(
          child: Text(
            'Inga larm har registrerats än',
            style: TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    // Beräkna statistik
    final totalAlarms = alarms.length;
    final redAlarms = alarms.where((a) => a.type == AlarmType.red).length;
    final yellowAlarms = alarms.where((a) => a.type == AlarmType.yellow).length;
    final resolvedAlarms = alarms.where((a) => a.status == AlarmStatus.resolved).length;
    final ongoingAlarms = alarms.where((a) => a.status == AlarmStatus.ongoing).length;
    final newAlarms = alarms.where((a) => a.status == AlarmStatus.new_).length;

    // Gruppera larm efter plats
    final locationMap = <String, int>{};
    for (final alarm in alarms) {
      locationMap[alarm.location] = (locationMap[alarm.location] ?? 0) + 1;
    }

    final locationEntries = locationMap.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Statistik'),
        actions: const [
          HomeButton(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Larmstatistik',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Sammanfattningskort
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Sammanfattning',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildStatRow('Totalt antal larm', totalAlarms, Colors.blue),
                    const Divider(),
                    _buildStatRow('Röda larm (akut)', redAlarms, Colors.red),
                    _buildStatRow('Gula larm (varning)', yellowAlarms, Colors.orange),
                    const Divider(),
                    _buildStatRow('Nya larm', newAlarms, Colors.purple),
                    _buildStatRow('Pågående larm', ongoingAlarms, Colors.blue),
                    _buildStatRow('Åtgärdade larm', resolvedAlarms, Colors.green),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Larm per plats
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Larm per plats',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...locationEntries.map((entry) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              entry.key,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: LinearProgressIndicator(
                              value: entry.value / totalAlarms,
                              backgroundColor: Colors.grey.shade200,
                              color: Colors.blue,
                              minHeight: 10,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${entry.value} (${(entry.value / totalAlarms * 100).toStringAsFixed(1)}%)',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Senaste larm
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Senaste larm',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...alarms.take(5).map((alarm) => ListTile(
                      leading: Icon(
                        Icons.warning_amber_rounded,
                        color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                      ),
                      title: Text(
                        '${alarm.type == AlarmType.red ? 'AKUT' : 'VARNING'}: ${alarm.location}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text(_formatDateTime(alarm.timestamp)),
                      trailing: _buildStatusBadge(alarm.status),
                      onTap: () => _viewAlarmDetails(alarm.id),
                    )),
                    const SizedBox(height: 8),
                    if (alarms.length > 5)
                      Center(
                        child: TextButton(
                          onPressed: () {
                            // Navigera till larmskärmen
                            Navigator.of(context).pushNamed('/alarms');
                          },
                          child: const Text('Visa alla larm'),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, int value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
          ),
          Text(
            value.toString(),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(AlarmStatus status) {
    Color color;
    String text;

    switch (status) {
      case AlarmStatus.new_:
        color = Colors.red;
        text = 'NYTT';
        break;
      case AlarmStatus.ongoing:
        color = Colors.blue;
        text = 'PÅGÅENDE';
        break;
      case AlarmStatus.resolved:
        color = Colors.green;
        text = 'ÅTGÄRDAT';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
}
