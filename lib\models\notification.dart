import 'package:cloud_firestore/cloud_firestore.dart';

class AppNotification {
  final String id;
  final String userId;
  final String? alertId;
  final String? storeId;
  final String? storeName;
  final String title;
  final String message;
  final DateTime createdAt;
  final bool read;

  AppNotification({
    required this.id,
    required this.userId,
    this.alertId,
    this.storeId,
    this.storeName,
    required this.title,
    required this.message,
    required this.createdAt,
    this.read = false,
  });

  AppNotification copyWith({
    String? id,
    String? userId,
    String? alertId,
    String? storeId,
    String? storeName,
    String? title,
    String? message,
    DateTime? createdAt,
    bool? read,
  }) {
    return AppNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      alertId: alertId ?? this.alertId,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      title: title ?? this.title,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      read: read ?? this.read,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'alertId': alertId,
      'storeId': storeId,
      'storeName': storeName,
      'title': title,
      'message': message,
      'createdAt': Timestamp.fromDate(createdAt),
      'read': read,
    };
  }

  factory AppNotification.fromMap(Map<String, dynamic> map) {
    return AppNotification(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      alertId: map['alertId'],
      storeId: map['storeId'],
      storeName: map['storeName'],
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      read: map['read'] ?? false,
    );
  }
}
