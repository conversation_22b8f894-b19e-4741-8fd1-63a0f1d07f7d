import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import '../lib/models/user.dart';
import '../lib/models/alert.dart';
import '../lib/models/button.dart';
import '../lib/models/store.dart';
import '../lib/services/auth_service.dart';
import '../lib/services/database_service.dart';
import '../lib/services/alert_service.dart';

@GenerateMocks([
  firebase_auth.FirebaseAuth,
  firebase_auth.User,
  firebase_auth.UserCredential,
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
  QuerySnapshot,
])
void main() {
  group('RBAC Tests', () {
    late MockFirebaseAuth mockFirebaseAuth;
    late MockFirebaseFirestore mockFirebaseFirestore;
    late MockUser mockUser;
    late MockUserCredential mockUserCredential;
    
    setUp(() async {
      mockFirebaseAuth = MockFirebaseAuth();
      mockFirebaseFirestore = MockFirebaseFirestore();
      mockUser = MockUser();
      mockUserCredential = MockUserCredential();
      
      // Set up mock behavior
      when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
      when(mockUser.uid).thenReturn('test-user-id');
      when(mockUser.email).thenReturn('<EMAIL>');
      when(mockUserCredential.user).thenReturn(mockUser);
      
      // Mock token result
      final mockIdTokenResult = MockIdTokenResult();
      when(mockUser.getIdTokenResult(any)).thenAnswer((_) async => mockIdTokenResult);
      when(mockIdTokenResult.claims).thenReturn({
        'role': 'admin',
        'stores': ['store-1', 'store-2'],
      });
    });
    
    test('Admin user can access all stores', () async {
      // Create a test admin user
      final adminUser = User(
        id: 'admin-id',
        username: 'admin',
        name: 'Admin User',
        role: UserRole.admin,
        email: '<EMAIL>',
        customClaims: {
          'role': 'admin',
        },
      );
      
      // Create test stores
      final store1 = Store(
        id: 'store-1',
        name: 'Store 1',
        address: 'Address 1',
        city: 'City 1',
        postalCode: '12345',
      );
      
      final store2 = Store(
        id: 'store-2',
        name: 'Store 2',
        address: 'Address 2',
        city: 'City 2',
        postalCode: '67890',
      );
      
      // Test that admin can access both stores
      expect(adminUser.isAdmin, true);
      expect(adminUser.customClaims?['role'], 'admin');
    });
    
    test('Guard can only access assigned stores', () async {
      // Create a test guard user
      final guardUser = User(
        id: 'guard-id',
        username: 'guard',
        name: 'Guard User',
        role: UserRole.guard,
        email: '<EMAIL>',
        stores: ['store-1'],
        customClaims: {
          'role': 'guard',
          'stores': ['store-1'],
        },
      );
      
      // Test that guard can only access assigned store
      expect(guardUser.isGuard, true);
      expect(guardUser.customClaims?['role'], 'guard');
      expect(guardUser.stores, contains('store-1'));
      expect(guardUser.stores, isNot(contains('store-2')));
    });
    
    test('Store user can only access their own store', () async {
      // Create a test store user
      final storeUser = User(
        id: 'store-id',
        username: 'store',
        name: 'Store User',
        role: UserRole.customer,
        email: '<EMAIL>',
        storeId: 'store-1',
        customClaims: {
          'role': 'store',
          'stores': ['store-1'],
        },
      );
      
      // Test that store user can only access their own store
      expect(storeUser.isCustomer, true);
      expect(storeUser.customClaims?['role'], 'store');
      expect(storeUser.storeId, 'store-1');
    });
  });
}

class MockIdTokenResult extends Mock implements firebase_auth.IdTokenResult {}
