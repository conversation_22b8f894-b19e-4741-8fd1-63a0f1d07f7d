import 'package:flutter/material.dart';
import '../../models/alarm_report.dart';
import '../../models/alarm.dart';
import '../../services/alarm_service.dart';
import '../../services/store_service.dart';
import '../../widgets/home_button.dart';
import '../alarm_detail_screen.dart';

class CustomerReportsScreen extends StatefulWidget {
  final String storeId;

  const CustomerReportsScreen({
    super.key,
    required this.storeId,
  });

  @override
  State<CustomerReportsScreen> createState() => _CustomerReportsScreenState();
}

class _CustomerReportsScreenState extends State<CustomerReportsScreen> {
  final AlarmService _alarmService = AlarmService();
  final StoreService _storeService = StoreService();

  List<AlarmReport> _reports = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReports();
  }

  void _loadReports() {
    setState(() {
      _isLoading = true;
    });

    try {
      // Hämta alla larm för butiken
      final storeAlarms = _alarmService.getAlarmsForStore(widget.storeId);

      // Hämta alla rapporter för dessa larm
      final storeReports = <AlarmReport>[];
      for (final alarm in storeAlarms) {
        try {
          final alarmReports = _alarmService.getReportsForAlarm(alarm.id);
          storeReports.addAll(alarmReports);
        } catch (e) {
          // Ignorera fel för enskilda larm
        }
      }

      // Sortera rapporterna efter datum (nyaste först)
      storeReports.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      setState(() {
        _reports = storeReports;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _reports = [];
        _isLoading = false;
      });
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final store = _storeService.getStoreById(widget.storeId);
    final storeName = store?.name ?? 'Okänd butik';

    return Scaffold(
      appBar: AppBar(
        title: Text('Rapporter - $storeName'),
        actions: const [
          HomeButton(),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : _reports.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.description_outlined,
                        size: 64,
                        color: Colors.blue,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Inga rapporter hittades',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Det finns inga rapporter för ${store?.name ?? 'denna butik'}.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: _loadReports,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Uppdatera'),
                      ),
                    ],
                  ),
                )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _reports.length,
              itemBuilder: (context, index) {
                final report = _reports[index];
                final alarm = _alarmService.getAlarmById(report.alarmId);

                if (alarm == null) {
                  return const SizedBox.shrink();
                }

                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Rapportrubrik
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: alarm.type == AlarmType.red
                              ? Colors.red.shade50
                              : Colors.orange.shade50,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            topRight: Radius.circular(4),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.warning,
                              color: alarm.type == AlarmType.red
                                  ? Colors.red
                                  : Colors.orange,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    alarm.type == AlarmType.red
                                        ? 'AKUT LARM'
                                        : 'VARNINGSLARM',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: alarm.type == AlarmType.red
                                          ? Colors.red
                                          : Colors.orange,
                                    ),
                                  ),
                                  Text(
                                    'Plats: ${alarm.location}',
                                    style: const TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Text(
                                'ÅTGÄRDAT',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Rapportinnehåll
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.person, size: 16),
                                const SizedBox(width: 4),
                                Text(
                                  'Rapport av: ${report.authorName}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                const Icon(Icons.access_time, size: 16),
                                const SizedBox(width: 4),
                                Text(
                                  'Tid: ${_formatDateTime(report.timestamp)}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'Åtgärd:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(report.content),
                            const SizedBox(height: 16),
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton.icon(
                                icon: const Icon(Icons.visibility),
                                label: const Text('Visa larmdetaljer'),
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => AlarmDetailScreen(
                                        alarmId: alarm.id,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
    );
  }
}
