import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/store.dart';
import '../models/iot_device.dart';
import '../models/guard_order.dart';
import 'auth_service.dart';

class StoreService {
  static final StoreService _instance = StoreService._internal();

  factory StoreService() {
    return _instance;
  }

  StoreService._internal();

  final AuthService _authService = AuthService();

  // Lista med vaktbeställningar (statisk för att delas mellan alla instanser)
  static final List<GuardOrder> _guardOrders = [];

  // Real stores will be fetched from Firestore
  final List<Store> _stores = [];

  // Real IoT devices will be fetched from Firestore
  final List<IoTDevice> _devices = [];

  // Hämta alla butiker
  List<Store> getAllStores() {
    return List.unmodifiable(_stores);
  }

  // Hämta butik med ID
  Store? getStoreById(String id) {
    try {
      return _stores.firstWhere((store) => store.id == id);
    } catch (e) {
      return null;
    }
  }

  // Hämta alla enheter
  List<IoTDevice> getAllDevices() {
    return List.unmodifiable(_devices);
  }

  // Hämta enhet med ID
  IoTDevice? getDeviceById(String id) {
    try {
      return _devices.firstWhere((device) => device.id == id);
    } catch (e) {
      return null;
    }
  }

  // Hämta enheter för en butik
  List<IoTDevice> getDevicesForStore(String storeId) {
    return _devices.where((device) => device.storeId == storeId).toList();
  }

  // Lägg till butik (endast för admin)
  bool addStore(Store store) {
    if (!_authService.isAdmin) return false;

    _stores.add(store);
    return true;
  }

  // Uppdatera butik (endast för admin)
  bool updateStore(Store store) {
    if (!_authService.isAdmin) return false;

    final index = _stores.indexWhere((s) => s.id == store.id);
    if (index != -1) {
      _stores[index] = store;
      return true;
    }
    return false;
  }

  // Lägg till enhet (endast för admin)
  bool addDevice(IoTDevice device) {
    if (!_authService.isAdmin) return false;

    _devices.add(device);

    // Uppdatera butikens deviceIds
    final storeIndex = _stores.indexWhere((s) => s.id == device.storeId);
    if (storeIndex != -1) {
      final store = _stores[storeIndex];
      final deviceIds = List<String>.from(store.deviceIds)..add(device.id);
      _stores[storeIndex] = store.copyWith(deviceIds: deviceIds);
    }

    return true;
  }

  // Uppdatera enhet (endast för admin)
  bool updateDevice(IoTDevice device) {
    if (!_authService.isAdmin) return false;

    final index = _devices.indexWhere((d) => d.id == device.id);
    if (index != -1) {
      _devices[index] = device;
      return true;
    }
    return false;
  }

  // Sök butiker
  List<Store> searchStores(String query) {
    if (query.isEmpty) return getAllStores();

    final lowercaseQuery = query.toLowerCase();
    return _stores.where((store) {
      return store.name.toLowerCase().contains(lowercaseQuery) ||
             store.address.toLowerCase().contains(lowercaseQuery) ||
             store.city.toLowerCase().contains(lowercaseQuery) ||
             store.contactPerson.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Sök enheter
  List<IoTDevice> searchDevices(String query) {
    if (query.isEmpty) return getAllDevices();

    final lowercaseQuery = query.toLowerCase();
    return _devices.where((device) {
      return device.name.toLowerCase().contains(lowercaseQuery) ||
             device.id.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Generera ett unikt ID
  String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
           Random().nextInt(1000).toString();
  }

  // Metoder för vaktbeställningar

  // Hämta alla beställningar
  List<GuardOrder> getAllGuardOrders() {
    // Returnera en kopia av listan istället för en oföränderlig lista
    // Detta gör att listan kan sorteras och modifieras av anroparen
    return List<GuardOrder>.from(_guardOrders);
  }

  // Hämta beställningar för en specifik butik
  List<GuardOrder> getGuardOrdersForStore(String storeId) {
    return _guardOrders.where((order) => order.storeId == storeId).toList();
  }

  // Hämta beställning med ID
  GuardOrder? getGuardOrderById(String id) {
    try {
      return _guardOrders.firstWhere((order) => order.id == id);
    } catch (e) {
      return null;
    }
  }

  // Skapa en ny beställning
  GuardOrder createGuardOrder({
    required String storeId,
    required DateTime requestedDate,
    required String startTime,
    required String endTime,
    required GuardType guardType,
    required int numberOfGuards,
    required String description,
    List<String> attachmentPaths = const [],
    String? securityGuardPermitPath,
  }) {
    final store = getStoreById(storeId);
    if (store == null) {
      throw Exception('Butiken hittades inte');
    }

    // Kontrollera om tillstånd krävs för ordningsvakt
    if (guardType == GuardType.securityGuard && securityGuardPermitPath == null) {
      throw Exception('Tillstånd krävs för ordningsvakt');
    }

    final order = GuardOrder(
      id: generateId(),
      storeId: storeId,
      storeName: store.name,
      createdAt: DateTime.now(),
      requestedDate: requestedDate,
      startTime: startTime,
      endTime: endTime,
      guardType: guardType,
      numberOfGuards: numberOfGuards,
      description: description,
      attachmentPaths: attachmentPaths,
      securityGuardPermitPath: securityGuardPermitPath,
    );

    _guardOrders.add(order);
    return order;
  }

  // Uppdatera en beställning (endast för admin)
  bool updateGuardOrder(GuardOrder order, {String? adminFeedback}) {
    if (!_authService.isAdmin) return false;

    final index = _guardOrders.indexWhere((o) => o.id == order.id);
    if (index != -1) {
      final updatedOrder = order.copyWith(
        updatedAt: DateTime.now(),
        adminFeedback: adminFeedback,
      );
      _guardOrders[index] = updatedOrder;
      return true;
    }
    return false;
  }

  // Lägg till bilagor till en beställning
  bool addAttachmentsToOrder(String orderId, List<String> attachmentPaths) {
    final index = _guardOrders.indexWhere((o) => o.id == orderId);
    if (index != -1) {
      final order = _guardOrders[index];
      final updatedAttachments = List<String>.from(order.attachmentPaths)..addAll(attachmentPaths);
      final updatedOrder = order.copyWith(
        attachmentPaths: updatedAttachments,
        updatedAt: DateTime.now(),
      );
      _guardOrders[index] = updatedOrder;
      return true;
    }
    return false;
  }

  // Lägg till tillstånd för ordningsvakt till en beställning
  bool addSecurityGuardPermitToOrder(String orderId, String permitPath) {
    final index = _guardOrders.indexWhere((o) => o.id == orderId);
    if (index != -1) {
      final order = _guardOrders[index];
      if (order.guardType == GuardType.securityGuard) {
        final updatedOrder = order.copyWith(
          securityGuardPermitPath: permitPath,
          updatedAt: DateTime.now(),
        );
        _guardOrders[index] = updatedOrder;
        return true;
      }
    }
    return false;
  }

  // Uppdatera en beställning direkt (endast för admin)
  bool updateOrderDirectly(GuardOrder updatedOrder) {
    // Kontrollera admin-behörighet
    if (!_authService.isAdmin) {
      debugPrint('Användaren är inte admin - isAdmin: ${_authService.isAdmin}');
      return false;
    }

    debugPrint('Admin-behörighet verifierad för direkt uppdatering');
    debugPrint('Söker efter beställning med ID: ${updatedOrder.id}');
    debugPrint('Antal beställningar i listan: ${_guardOrders.length}');

    final index = _guardOrders.indexWhere((o) => o.id == updatedOrder.id);
    if (index != -1) {
      // Skriv ut debug-information
      final oldOrder = _guardOrders[index];
      debugPrint('Hittade beställning: ${oldOrder.id}');
      debugPrint('Gammal status: ${oldOrder.status}');
      debugPrint('Ny status: ${updatedOrder.status}');
      debugPrint('Gammal feedback: ${oldOrder.adminFeedback}');
      debugPrint('Ny feedback: ${updatedOrder.adminFeedback}');

      // Skapa en statusändring om statusen har ändrats
      if (oldOrder.status != updatedOrder.status) {
        final currentUser = _authService.currentUser;
        final statusChange = OrderStatusChange(
          timestamp: DateTime.now(),
          fromStatus: oldOrder.status,
          toStatus: updatedOrder.status,
          userId: currentUser?.id,
          userName: currentUser?.name,
          note: updatedOrder.adminFeedback != oldOrder.adminFeedback
              ? 'Feedback uppdaterad: ${updatedOrder.adminFeedback}'
              : 'Status ändrad från ${oldOrder.statusText} till ${updatedOrder.statusText}',
        );

        // Skapa en ny lista med statushistorik
        final newHistory = List<OrderStatusChange>.from(updatedOrder.statusHistory)
          ..add(statusChange);

        // Uppdatera beställningen med den nya historiken
        final finalOrder = updatedOrder.copyWith(
          statusHistory: newHistory,
        );

        // Uppdatera beställningen i listan
        _guardOrders[index] = finalOrder;
      } else {
        // Uppdatera beställningen i listan utan att lägga till statushistorik
        _guardOrders[index] = updatedOrder;
      }

      // Verifiera att uppdateringen lyckades
      final verifyOrder = _guardOrders[index];
      debugPrint('Verifierad status efter uppdatering: ${verifyOrder.status}');
      debugPrint('Verifierad feedback efter uppdatering: ${verifyOrder.adminFeedback}');
      debugPrint('Antal statusändringar i historik: ${verifyOrder.statusHistory.length}');

      return true;
    } else {
      debugPrint('Hittade inte beställning med ID: ${updatedOrder.id}');
      return false;
    }
  }

  // Ändra status på en beställning (endast för admin)
  bool updateGuardOrderStatus(String orderId, OrderStatus status, {String? adminFeedback}) {
    // Kontrollera admin-behörighet
    if (!_authService.isAdmin) {
      debugPrint('Användaren är inte admin - isAdmin: ${_authService.isAdmin}');
      return false;
    }

    debugPrint('Admin-behörighet verifierad');
    debugPrint('Söker efter beställning med ID: $orderId');
    debugPrint('Antal beställningar i listan: ${_guardOrders.length}');

    // Skriv ut alla beställningar för felsökning
    for (var i = 0; i < _guardOrders.length; i++) {
      debugPrint('Beställning $i: ID=${_guardOrders[i].id}, Status=${_guardOrders[i].status}');
    }

    final index = _guardOrders.indexWhere((o) => o.id == orderId);
    if (index != -1) {
      final order = _guardOrders[index];

      // Skriv ut debug-information
      debugPrint('Hittade beställning: ${order.id}');
      debugPrint('Nuvarande status: ${order.status}');
      debugPrint('Ny status: $status');
      debugPrint('Nuvarande feedback: ${order.adminFeedback}');
      debugPrint('Ny feedback: $adminFeedback');

      // Skapa en statusändring
      final currentUser = _authService.currentUser;
      final statusChange = OrderStatusChange(
        timestamp: DateTime.now(),
        fromStatus: order.status,
        toStatus: status,
        userId: currentUser?.id,
        userName: currentUser?.name,
        note: adminFeedback != null && adminFeedback != order.adminFeedback
            ? 'Feedback: $adminFeedback'
            : 'Status ändrad från ${order.statusText} till ${_getStatusText(status)}',
      );

      // Skapa en ny lista med statushistorik
      final newHistory = List<OrderStatusChange>.from(order.statusHistory)
        ..add(statusChange);

      // Skapa en uppdaterad version av beställningen
      final updatedOrder = order.copyWith(
        status: status,
        updatedAt: DateTime.now(),
        adminFeedback: adminFeedback ?? order.adminFeedback,
        statusHistory: newHistory,
      );

      // Skriv ut debug-information om den uppdaterade beställningen
      debugPrint('Uppdaterad beställning - Status: ${updatedOrder.status}');
      debugPrint('Uppdaterad beställning - Feedback: ${updatedOrder.adminFeedback}');

      // Uppdatera beställningen i listan
      _guardOrders[index] = updatedOrder;

      // Verifiera att uppdateringen lyckades
      final verifyOrder = _guardOrders[index];
      debugPrint('Verifierad status efter uppdatering: ${verifyOrder.status}');
      debugPrint('Verifierad feedback efter uppdatering: ${verifyOrder.adminFeedback}');

      return true;
    } else {
      debugPrint('Hittade inte beställning med ID: $orderId');
      return false;
    }
  }

  // Hjälpmetod för att få statustext
  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Väntande';
      case OrderStatus.approved:
        return 'Godkänd';
      case OrderStatus.rejected:
        return 'Avvisad';
      case OrderStatus.completed:
        return 'Genomförd';
      case OrderStatus.cancelled:
        return 'Avbruten';
    }
  }

  // Avbryt en beställning (för kunder)
  bool cancelGuardOrder(String orderId) {
    final index = _guardOrders.indexWhere((o) => o.id == orderId);
    if (index != -1) {
      final order = _guardOrders[index];

      // Kontrollera att det är kundens egen beställning
      final currentUser = _authService.currentUser;
      if (currentUser == null || currentUser.storeId != order.storeId) {
        return false;
      }

      // Kontrollera att beställningen kan avbrytas
      if (order.status == OrderStatus.completed) {
        return false;
      }

      // Skapa en statusändring
      final statusChange = OrderStatusChange(
        timestamp: DateTime.now(),
        fromStatus: order.status,
        toStatus: OrderStatus.cancelled,
        userId: currentUser.id,
        userName: currentUser.name,
        note: 'Beställning avbruten av kund',
      );

      // Skapa en ny lista med statushistorik
      final newHistory = List<OrderStatusChange>.from(order.statusHistory)
        ..add(statusChange);

      final updatedOrder = order.copyWith(
        status: OrderStatus.cancelled,
        updatedAt: DateTime.now(),
        statusHistory: newHistory,
      );
      _guardOrders[index] = updatedOrder;
      return true;
    }
    return false;
  }
}
