import 'package:cloud_firestore/cloud_firestore.dart';

class FirestoreService {
  static final FirestoreService _instance = FirestoreService._internal();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  factory FirestoreService() {
    return _instance;
  }

  FirestoreService._internal();

  // Collection references
  CollectionReference get usersCollection => _firestore.collection('users');
  CollectionReference get storesCollection => _firestore.collection('stores');
  CollectionReference get alarmsCollection => _firestore.collection('alarms');
  CollectionReference get devicesCollection => _firestore.collection('devices');
  CollectionReference get guardOrdersCollection => _firestore.collection('guardOrders');
  CollectionReference get reportsCollection => _firestore.collection('reports');
  CollectionReference get activityLogsCollection => _firestore.collection('activityLogs');

  // Generic CRUD operations
  Future<DocumentReference> addDocument(CollectionReference collection, Map<String, dynamic> data) async {
    return await collection.add(data);
  }

  Future<void> setDocument(CollectionReference collection, String documentId, Map<String, dynamic> data) async {
    await collection.doc(documentId).set(data);
  }

  Future<void> updateDocument(CollectionReference collection, String documentId, Map<String, dynamic> data) async {
    await collection.doc(documentId).update(data);
  }

  Future<void> deleteDocument(CollectionReference collection, String documentId) async {
    await collection.doc(documentId).delete();
  }

  Future<DocumentSnapshot> getDocument(CollectionReference collection, String documentId) async {
    return await collection.doc(documentId).get();
  }

  Future<QuerySnapshot> getCollection(CollectionReference collection) async {
    return await collection.get();
  }

  Stream<DocumentSnapshot> documentStream(CollectionReference collection, String documentId) {
    return collection.doc(documentId).snapshots();
  }

  Stream<QuerySnapshot> collectionStream(CollectionReference collection) {
    return collection.snapshots();
  }

  // User-specific operations
  Future<void> setUser(String userId, Map<String, dynamic> userData) async {
    await usersCollection.doc(userId).set(userData);
  }

  Future<DocumentSnapshot> getUser(String userId) async {
    return await usersCollection.doc(userId).get();
  }

  Future<QuerySnapshot> getUsersByRole(int roleIndex) async {
    return await usersCollection.where('role', isEqualTo: roleIndex).get();
  }

  // Store-specific operations
  Future<void> setStore(String storeId, Map<String, dynamic> storeData) async {
    await storesCollection.doc(storeId).set(storeData);
  }

  Future<DocumentSnapshot> getStore(String storeId) async {
    return await storesCollection.doc(storeId).get();
  }

  // Alarm-specific operations
  Future<void> setAlarm(String alarmId, Map<String, dynamic> alarmData) async {
    await alarmsCollection.doc(alarmId).set(alarmData);
  }

  Future<DocumentSnapshot> getAlarm(String alarmId) async {
    return await alarmsCollection.doc(alarmId).get();
  }

  Future<QuerySnapshot> getAlarmsByStore(String storeId) async {
    return await alarmsCollection.where('storeId', isEqualTo: storeId).get();
  }

  Stream<QuerySnapshot> alarmStreamByStore(String storeId) {
    return alarmsCollection.where('storeId', isEqualTo: storeId).snapshots();
  }

  // Device-specific operations
  Future<void> setDevice(String deviceId, Map<String, dynamic> deviceData) async {
    await devicesCollection.doc(deviceId).set(deviceData);
  }

  Future<DocumentSnapshot> getDevice(String deviceId) async {
    return await devicesCollection.doc(deviceId).get();
  }

  Future<QuerySnapshot> getDevicesByStore(String storeId) async {
    return await devicesCollection.where('storeId', isEqualTo: storeId).get();
  }

  // Guard order-specific operations
  Future<void> setGuardOrder(String orderId, Map<String, dynamic> orderData) async {
    await guardOrdersCollection.doc(orderId).set(orderData);
  }

  Future<DocumentSnapshot> getGuardOrder(String orderId) async {
    return await guardOrdersCollection.doc(orderId).get();
  }

  Future<QuerySnapshot> getGuardOrdersByStore(String storeId) async {
    return await guardOrdersCollection.where('storeId', isEqualTo: storeId).get();
  }

  // Report-specific operations
  Future<void> setReport(String reportId, Map<String, dynamic> reportData) async {
    await reportsCollection.doc(reportId).set(reportData);
  }

  Future<DocumentSnapshot> getReport(String reportId) async {
    return await reportsCollection.doc(reportId).get();
  }

  Future<QuerySnapshot> getReportsByStore(String storeId) async {
    return await reportsCollection.where('storeId', isEqualTo: storeId).get();
  }

  // Activity log-specific operations
  Future<void> addActivityLog(Map<String, dynamic> logData) async {
    await activityLogsCollection.add(logData);
  }

  Future<QuerySnapshot> getActivityLogsByUser(String userId) async {
    return await activityLogsCollection.where('userId', isEqualTo: userId).get();
  }

  // Transaction example (for complex operations)
  Future<void> runTransaction(Function(Transaction) updateFunction) async {
    await _firestore.runTransaction((transaction) async {
      return await updateFunction(transaction);
    });
  }

  // Batch write example (for multiple operations)
  Future<void> batchWrite(Function(WriteBatch) updateFunction) async {
    WriteBatch batch = _firestore.batch();
    updateFunction(batch);
    await batch.commit();
  }
}
