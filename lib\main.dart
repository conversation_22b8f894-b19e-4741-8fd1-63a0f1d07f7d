import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'screens/alarm_screen.dart';
import 'screens/report_screen.dart';
import 'screens/alarm_detail_screen.dart';
import 'screens/alert_detail_screen.dart';
import 'screens/statistics_screen.dart';
import 'screens/login_screen.dart';
import 'screens/admin/admin_dashboard.dart';
import 'screens/guard/guard_dashboard.dart';
import 'screens/customer/customer_dashboard.dart';
import 'screens/guard/active_guards_screen.dart';
import 'screens/guard/device_status_screen.dart';
import 'screens/guard/store_search_screen.dart';
import 'screens/admin/user_management_screen.dart';
import 'screens/customer/guard_orders_screen.dart';
import 'screens/admin/manage_guard_orders_screen.dart';
import 'services/auth_service.dart';
import 'services/app_lifecycle_observer.dart';
import 'services/global_alert_listener.dart';
import 'models/alarm.dart';
import 'screens/alert_queue_screen.dart';

import 'theme/security_theme.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize AuthService
  final authService = AuthService();
  authService.initialize();

  // Create the app lifecycle observer to manage alert listeners
  // This will automatically start and stop the alert listener based on app lifecycle
  final lifecycleObserver = AppLifecycleObserver();

  // IoT simulator disabled - using real data from Firestore
  // final iotSimulator = IoTDeviceSimulator();
  // iotSimulator.stopSimulation();
  // iotSimulator.startSimulation();

  runApp(ParatusApp(lifecycleObserver: lifecycleObserver));
}

class ParatusApp extends StatefulWidget {
  final AppLifecycleObserver lifecycleObserver;

  const ParatusApp({super.key, required this.lifecycleObserver});

  // Static method to force navigation to the alert screen from anywhere in the app
  static void navigateToAlertScreen(BuildContext context, Alarm alarm) {
    // First try to pop any current dialogs that might be blocking navigation
    Navigator.of(context, rootNavigator: true).popUntil((route) => route.isFirst);

    // Then push our alert screen
    Navigator.of(context, rootNavigator: true).push(
      MaterialPageRoute(
        builder: (context) => AlertQueueScreen(alarm: alarm),
        fullscreenDialog: true,
      ),
    );
  }

  @override
  State<ParatusApp> createState() => _ParatusAppState();
}

class _ParatusAppState extends State<ParatusApp> {
  final AuthService _authService = AuthService();
  StreamSubscription? _authSubscription;

  @override
  void initState() {
    super.initState();

    // Lyssna på ändringar i autentiseringsstatus
    _authSubscription = _authService.authStateChanges.listen((user) {
      if (mounted) {
        setState(() {});

        // No need to manually start/stop the alert listener here
        // The AppLifecycleObserver will handle that based on app lifecycle
      }
    });
  }

  @override
  void dispose() {
    // Avsluta prenumerationen när appen stängs
    _authSubscription?.cancel();

    // Dispose the lifecycle observer
    widget.lifecycleObserver.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: AuthService.navigatorKey, // Använd navigatorKey från AuthService
      debugShowCheckedModeBanner: false,
      title: 'Paratus Vakt',
      theme: SecurityTheme.themeData(),
      // Use a Builder to ensure we have a valid context for the entire app
      home: Builder(
        builder: (BuildContext context) {
          // Return the appropriate home screen based on login state
          return _authService.isLoggedIn
              ? _authService.isAdmin
                  ? const AdminDashboard()
                  : _authService.isGuard
                      ? const GuardDashboard()
                      : const CustomerDashboard()
              : LoginScreen(
                  onLoginSuccess: (_) {
                    setState(() {});
                  },
                );
        },
      ),
      routes: {
        '/login': (context) => Builder(
              builder: (context) => LoginScreen(
                onLoginSuccess: (_) {
                  setState(() {});
                },
              ),
            ),
        '/admin_dashboard': (context) => Builder(
              builder: (context) => const AdminDashboard(),
            ),
        '/guard_dashboard': (context) => Builder(
              builder: (context) => const GuardDashboard(),
            ),
        '/customer_dashboard': (context) => Builder(
              builder: (context) => const CustomerDashboard(),
            ),
        '/alarm_detail': (context) => Builder(
              builder: (context) {
                final args = ModalRoute.of(context)!.settings.arguments as String;
                return AlarmDetailScreen(alarmId: args);
              },
            ),
        '/alert_detail': (context) => Builder(
              builder: (context) {
                final args = ModalRoute.of(context)!.settings.arguments as String;
                return AlertDetailScreen(alertId: args);
              },
            ),
        '/alarms': (context) => Builder(
              builder: (context) => const AlarmScreen(),
            ),
        '/reports': (context) => Builder(
              builder: (context) => const ReportScreen(),
            ),
        '/statistics': (context) => Builder(
              builder: (context) => const StatisticsScreen(),
            ),
        '/active_guards': (context) => Builder(
              builder: (context) => const ActiveGuardsScreen(),
            ),
        '/user_management': (context) => Builder(
              builder: (context) => const UserManagementScreen(),
            ),
        '/device_status': (context) => Builder(
              builder: (context) => const DeviceStatusScreen(),
            ),
        '/store_search': (context) => Builder(
              builder: (context) => const StoreSearchScreen(),
            ),
        '/guard_orders': (context) => Builder(
              builder: (context) {
                final args = ModalRoute.of(context)!.settings.arguments as String;
                return GuardOrdersScreen(storeId: args);
              },
            ),
        '/manage_guard_orders': (context) => Builder(
              builder: (context) => const ManageGuardOrdersScreen(),
            ),
      },
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color.fromARGB(255, 23, 166, 214),
      child: const Center(
        child: Text(
          'Paratus',
          style: TextStyle(fontSize: 20, color: Colors.white),
        ),
      ),
    );
  }
}

class TeamScreen extends StatelessWidget {
  const TeamScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Här visas ditt team.',
        style: TextStyle(fontSize: 20),
      ),
    );
  }
}

class TasksScreen extends StatelessWidget {
  const TasksScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Här listas dina uppgifter.',
        style: TextStyle(fontSize: 20),
      ),
    );
  }
}

// AlarmScreen har flyttats till lib/screens/alarm_screen.dart

// ReportScreen har flyttats till lib/screens/report_screen.dart

class ScheduleScreen extends StatelessWidget {
  ScheduleScreen({super.key});

  // Real schedule data will be fetched from Firestore
  final List<Map<String, dynamic>> schedule = [];

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: schedule.length,
      itemBuilder: (context, index) {
        final day = schedule[index];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${day['day']} • ${day['date']}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...day['shifts'].map<Widget>((shift) {
              final time = shift['time'];
              final isFree = time.toLowerCase() == 'ledig';
              return Card(
                color: isFree ? Colors.grey[200] : Colors.blue[50],
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  leading: Icon(isFree ? Icons.free_breakfast : Icons.schedule),
                  title: Text(time),
                  subtitle: isFree
                      ? const Text('Ingen tjänst')
                      : Text('${shift['location']} – ${shift['role']}'),
                ),
              );
            }).toList(),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }
}
