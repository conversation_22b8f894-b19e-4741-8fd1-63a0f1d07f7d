import 'dart:io';
import 'package:flutter/material.dart';
// import 'package:open_file/open_file.dart';
import '../services/file_service.dart';

class AttachmentList extends StatelessWidget {
  final List<String> attachmentPaths;
  final Function(String)? onRemove;
  final bool readOnly;

  const AttachmentList({
    super.key,
    required this.attachmentPaths,
    this.onRemove,
    this.readOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    if (attachmentPaths.isEmpty) {
      return const Center(
        child: Text(
          'Inga bilagor',
          style: TextStyle(
            fontStyle: FontStyle.italic,
            color: Colors.grey,
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: attachmentPaths.length,
      itemBuilder: (context, index) {
        final filePath = attachmentPaths[index];
        final fileName = FileService().getFileName(filePath);
        final isImage = FileService().isImage(filePath);
        final isPdf = FileService().isPdf(filePath);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.grey.shade200,
              child: Icon(
                isImage ? Icons.image :
                isPdf ? Icons.picture_as_pdf :
                Icons.insert_drive_file,
                color: isImage ? Colors.blue :
                       isPdf ? Colors.red :
                       Colors.orange,
              ),
            ),
            title: Text(
              fileName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              isImage ? 'Bild' :
              isPdf ? 'PDF-dokument' :
              'Dokument',
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
            trailing: readOnly ? null : IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () {
                if (onRemove != null) {
                  onRemove!(filePath);
                }
              },
            ),
            onTap: () async {
              // Mock implementation - just show a message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Öppnar fil: $fileName'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
          ),
        );
      },
    );
  }
}
