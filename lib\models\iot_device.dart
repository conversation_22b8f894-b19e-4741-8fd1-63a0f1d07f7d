enum DeviceStatus { active, inactive, maintenance }

class IoTDevice {
  final String id;
  final String name;
  final String storeId;
  final int batteryLevel; // 0-100
  final DateTime lastActive;
  final DeviceStatus status;
  
  IoTDevice({
    required this.id,
    required this.name,
    required this.storeId,
    required this.batteryLevel,
    required this.lastActive,
    this.status = DeviceStatus.active,
  });
  
  bool get isLowBattery => batteryLevel < 20;
  bool get isActive => status == DeviceStatus.active;
  
  IoTDevice copyWith({
    String? id,
    String? name,
    String? storeId,
    int? batteryLevel,
    DateTime? lastActive,
    DeviceStatus? status,
  }) {
    return IoTDevice(
      id: id ?? this.id,
      name: name ?? this.name,
      storeId: storeId ?? this.storeId,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      lastActive: lastActive ?? this.lastActive,
      status: status ?? this.status,
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'storeId': storeId,
      'batteryLevel': batteryLevel,
      'lastActive': lastActive.millisecondsSinceEpoch,
      'status': status.index,
    };
  }
  
  factory IoTDevice.fromMap(Map<String, dynamic> map) {
    return IoTDevice(
      id: map['id'],
      name: map['name'],
      storeId: map['storeId'],
      batteryLevel: map['batteryLevel'],
      lastActive: DateTime.fromMillisecondsSinceEpoch(map['lastActive']),
      status: DeviceStatus.values[map['status'] ?? 0],
    );
  }
}
