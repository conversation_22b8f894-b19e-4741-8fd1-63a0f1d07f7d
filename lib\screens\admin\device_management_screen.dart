import 'package:flutter/material.dart';
import '../../models/iot_device.dart';
import '../../models/store.dart';
import '../../services/store_service.dart';

class DeviceManagementScreen extends StatefulWidget {
  const DeviceManagementScreen({super.key});

  @override
  State<DeviceManagementScreen> createState() => _DeviceManagementScreenState();
}

class _DeviceManagementScreenState extends State<DeviceManagementScreen> {
  final StoreService _storeService = StoreService();
  final TextEditingController _searchController = TextEditingController();

  List<IoTDevice> _devices = [];
  List<IoTDevice> _filteredDevices = [];
  Map<String, Store> _storeMap = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadData() {
    _devices = _storeService.getAllDevices();
    _filteredDevices = List.from(_devices);

    // Skapa en map av butiker för snabb uppslag
    final stores = _storeService.getAllStores();
    _storeMap = {for (var store in stores) store.id: store};

    setState(() {});
  }

  void _filterDevices(String query) {
    if (query.isEmpty) {
      _filteredDevices = List.from(_devices);
    } else {
      _filteredDevices = _storeService.searchDevices(query);
    }
    setState(() {});
  }

  String _getStoreName(String storeId) {
    return _storeMap[storeId]?.name ?? 'Okänd butik';
  }

  Color _getBatteryColor(int level) {
    if (level <= 10) return Colors.red;
    if (level <= 20) return Colors.orange;
    if (level <= 50) return Colors.yellow;
    return Colors.green;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhetshantering'),
      ),
      body: Column(
        children: [
          // Sökfält
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Sök enheter',
                hintText: 'Ange namn eller ID...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterDevices('');
                        },
                      )
                    : null,
              ),
              onChanged: _filterDevices,
            ),
          ),

          // Statistik
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      'Totalt',
                      _devices.length.toString(),
                      Icons.devices,
                      Colors.blue,
                    ),
                    _buildStatItem(
                      'Aktiva',
                      _devices.where((d) => d.status == DeviceStatus.active).length.toString(),
                      Icons.check_circle,
                      Colors.green,
                    ),
                    _buildStatItem(
                      'Lågt batteri',
                      _devices.where((d) => d.isLowBattery).length.toString(),
                      Icons.battery_alert,
                      Colors.red,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Enhetslista
          Expanded(
            child: _filteredDevices.isEmpty
                ? const Center(
                    child: Text(
                      'Inga enheter hittades',
                      style: TextStyle(fontSize: 16),
                    ),
                  )
                : ListView.builder(
                    itemCount: _filteredDevices.length,
                    itemBuilder: (context, index) {
                      final device = _filteredDevices[index];
                      final storeName = _getStoreName(device.storeId);

                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: Column(
                          children: [
                            ListTile(
                              leading: Icon(
                                Icons.device_hub,
                                color: device.isActive
                                    ? Colors.green
                                    : Colors.grey,
                                size: 32,
                              ),
                              title: Text(
                                device.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              subtitle: Text(
                                'ID: ${device.id}\nButik: $storeName',
                              ),
                              trailing: Icon(
                                Icons.circle,
                                color: device.status == DeviceStatus.active
                                    ? Colors.green
                                    : device.status == DeviceStatus.maintenance
                                        ? Colors.orange
                                        : Colors.red,
                              ),
                              isThreeLine: true,
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16.0,
                                vertical: 8.0,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Text('Batteristatus:'),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: LinearProgressIndicator(
                                          value: device.batteryLevel / 100,
                                          backgroundColor: Colors.grey.shade200,
                                          color: _getBatteryColor(device.batteryLevel),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        '${device.batteryLevel}%',
                                        style: TextStyle(
                                          fontWeight: device.isLowBattery
                                              ? FontWeight.bold
                                              : null,
                                          color: device.isLowBattery
                                              ? Colors.red
                                              : null,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Senast aktiv: ${_formatDateTime(device.lastActive)}',
                                    style: TextStyle(
                                      color: DateTime.now().difference(device.lastActive).inDays > 7
                                          ? Colors.red
                                          : null,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Status: ${_getStatusText(device.status)}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: device.status == DeviceStatus.active
                                          ? Colors.green
                                          : device.status == DeviceStatus.maintenance
                                              ? Colors.orange
                                              : Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            ButtonBar(
                              children: [
                                TextButton(
                                  onPressed: () {
                                    // Här skulle vi normalt visa en dialog för att redigera enheten
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Redigera enhet (kommer snart)'),
                                      ),
                                    );
                                  },
                                  child: const Text('REDIGERA'),
                                ),
                                TextButton(
                                  onPressed: () {
                                    // Här skulle vi normalt visa en dialog för att ändra status
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Ändra status (kommer snart)'),
                                      ),
                                    );
                                  },
                                  child: const Text('ÄNDRA STATUS'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddDeviceDialog(context);
        },
        tooltip: 'Lägg till ny enhet',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getStatusText(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.active:
        return 'Aktiv';
      case DeviceStatus.inactive:
        return 'Inaktiv';
      case DeviceStatus.maintenance:
        return 'Underhåll';
    }
  }

  void _showAddDeviceDialog(BuildContext context) {
    final nameController = TextEditingController();
    final idController = TextEditingController();
    String? selectedStoreId;
    DeviceStatus selectedStatus = DeviceStatus.active;

    final stores = _storeService.getAllStores();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Lägg till ny enhet'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Enhetsnamn',
                  hintText: 'T.ex. Larmknapp Entré',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: idController,
                decoration: const InputDecoration(
                  labelText: 'Enhets-ID',
                  hintText: 'T.ex. device_123',
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Välj butik',
                ),
                hint: const Text('Välj butik'),
                items: stores.map((store) {
                  return DropdownMenuItem<String>(
                    value: store.id,
                    child: Text(store.name),
                  );
                }).toList(),
                onChanged: (value) {
                  selectedStoreId = value;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<DeviceStatus>(
                decoration: const InputDecoration(
                  labelText: 'Status',
                ),
                value: selectedStatus,
                items: DeviceStatus.values.map((status) {
                  return DropdownMenuItem<DeviceStatus>(
                    value: status,
                    child: Text(_getStatusText(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    selectedStatus = value;
                  }
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Avbryt'),
          ),
          ElevatedButton(
            onPressed: () {
              // Validera inmatning
              if (nameController.text.isEmpty ||
                  idController.text.isEmpty ||
                  selectedStoreId == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Alla fält måste fyllas i'),
                  ),
                );
                return;
              }

              // Skapa ny enhet
              final newDevice = IoTDevice(
                id: idController.text,
                name: nameController.text,
                storeId: selectedStoreId!,
                status: selectedStatus,
                batteryLevel: 100, // Ny enhet har fullt batteri
                lastActive: DateTime.now(),
              );

              // Lägg till enheten
              _storeService.addDevice(newDevice);

              // Uppdatera listan
              _loadData();

              // Stäng dialogen
              Navigator.pop(context);

              // Visa bekräftelse
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Enheten ${newDevice.name} har lagts till'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Lägg till'),
          ),
        ],
      ),
    );
  }
}
