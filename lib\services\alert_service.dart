import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/alert.dart';
import '../models/user.dart';
import '../models/store.dart';
import '../models/button.dart';
import '../models/notification.dart';
import '../utils/alert_status_utils.dart';
import 'database_service.dart';
import 'auth_service.dart';

class AlertService {
  static final AlertService _instance = AlertService._internal();
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();

  final _alertStreamController = StreamController<Alert>.broadcast();
  Stream<Alert> get alertStream => _alertStreamController.stream;

  factory AlertService() {
    return _instance;
  }

  AlertService._internal();

  // Get all alerts
  Future<List<Alert>> getAllAlerts() async {
    try {
      final snapshot = await _databaseService.getAlertsStream().first;
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Alert.fromMap({
          'id': doc.id,
          ...data,
        });
      }).toList();
    } catch (e) {
      print('Error getting alerts: $e');
      return [];
    }
  }

  // Get alerts for a specific store
  Future<List<Alert>> getAlertsByStore(String storeId) async {
    try {
      final snapshot = await _databaseService.getAlertsByStore(storeId);
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Alert.fromMap({
          'id': doc.id,
          ...data,
        });
      }).toList();
    } catch (e) {
      print('Error getting alerts for store: $e');
      return [];
    }
  }

  // Get all alerts
  Stream<List<Alert>> getAllAlertsStream() {
    return _databaseService.getAlertsStream().map((snapshot) {
      // This will be called whenever there are any changes to the collection
      // including document additions, modifications, and deletions
      print('Alert stream received update with ${snapshot.docs.length} documents');

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Alert.fromMap({
          'id': doc.id,
          ...data,
        });
      }).toList();
    }).handleError((error) {
      print('Error in getAllAlertsStream: $error');
      // Re-throw the error so it can be caught by listeners
      throw error;
    });
  }

  // Get active alerts
  Stream<List<Alert>> getActiveAlertsStream() {
    return _databaseService.getActiveAlertsStream().map((snapshot) {
      // This will be called whenever there are any changes to the active alerts
      // including document additions, modifications, and deletions
      print('Active alert stream received update with ${snapshot.docs.length} documents');

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Alert.fromMap({
          'id': doc.id,
          ...data,
        });
      }).toList();
    }).handleError((error) {
      print('Error in getActiveAlertsStream: $error');
      // Re-throw the error so it can be caught by listeners
      throw error;
    });
  }

  // Get a specific alert
  Future<Alert?> getAlert(String alertId) async {
    try {
      final doc = await _databaseService.getAlert(alertId);
      if (!doc.exists) return null;

      final data = doc.data() as Map<String, dynamic>;
      return Alert.fromMap({
        'id': doc.id,
        ...data,
      });
    } catch (e) {
      print('Error getting alert: $e');
      return null;
    }
  }

  // Create a new alert
  Future<Alert?> createAlert(String buttonId, String storeId) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return null;

      final alertData = {
        'buttonId': buttonId,
        'storeId': storeId,
        'pressedAt': Timestamp.now(),
        'status': 'active',
      };

      final docRef = await _databaseService.addAlert(alertData);

      // Log activity
      await _databaseService.addActivity({
        'userId': currentUser.id,
        'action': 'alert_created',
        'timestamp': Timestamp.now(),
        'meta': {
          'alertId': docRef.id,
          'buttonId': buttonId,
          'storeId': storeId,
        },
      });

      // Get the created alert
      final alert = await getAlert(docRef.id);

      // Notify listeners
      if (alert != null) {
        _alertStreamController.add(alert);
      }

      return alert;
    } catch (e) {
      print('Error creating alert: $e');
      return null;
    }
  }

  // Helper method to convert AlertStatus to string
  String alertStatusToString(AlertStatus status) {
    return AlertStatusUtils.statusToString(status);
  }

  // Update alert status
  Future<bool> updateAlertStatus(String alertId, AlertStatus status, {String? note}) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return false;

      // Get the current alert
      final alert = await getAlert(alertId);
      if (alert == null) return false;

      // Create status change
      final statusChange = AlertStatusChange(
        timestamp: DateTime.now(),
        fromStatus: alert.status,
        toStatus: status,
        userId: currentUser.id,
        userName: currentUser.name,
        note: note,
      );

      // Update alert data
      final updateData = {
        'status': alertStatusToString(status),
        'handledBy': currentUser.id,
        'handlerName': currentUser.name,
        'statusHistory': FieldValue.arrayUnion([statusChange.toMap()]),
      };

      // Add resolvedAt if status is resolved or false_alarm
      if (status == AlertStatus.resolved || status == AlertStatus.false_alarm) {
        updateData['resolvedAt'] = Timestamp.now();
      }

      await _databaseService.updateAlert(alertId, updateData);

      // Log activity
      await _databaseService.addActivity({
        'userId': currentUser.id,
        'action': 'alert_status_updated',
        'timestamp': Timestamp.now(),
        'meta': {
          'alertId': alertId,
          'fromStatus': alertStatusToString(alert.status),
          'toStatus': alertStatusToString(status),
          'note': note,
        },
      });

      return true;
    } catch (e) {
      print('Error updating alert status: $e');
      return false;
    }
  }

  // Get alerts assigned to a guard
  Future<List<Alert>> getAlertsAssignedToGuard(String guardId) async {
    try {
      final snapshot = await _databaseService.alertsCollection
          .where('handledBy', isEqualTo: guardId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Alert.fromMap({
          'id': doc.id,
          ...data,
        });
      }).toList();
    } catch (e) {
      print('Error getting alerts assigned to guard: $e');
      return [];
    }
  }

  // Dispose resources
  void dispose() {
    _alertStreamController.close();
  }
}
