import 'dart:async';
import 'dart:math';
import '../models/user.dart';
import '../models/alarm.dart';
import 'auth_service.dart';
import 'alarm_service.dart';

class UserService {
  static final UserService _instance = UserService._internal();

  factory UserService() {
    return _instance;
  }

  UserService._internal();

  final AuthService _authService = AuthService();
  final AlarmService _alarmService = AlarmService();

  // Real users will be fetched from Firestore
  final List<User> _users = [];

  // No hardcoded passwords
  final Map<String, String> _passwords = {};

  // Hämta alla användare
  List<User> getAllUsers() {
    return List.unmodifiable(_users);
  }

  // Hämta aktiva vakter
  List<User> getActiveGuards() {
    return _users.where((user) =>
      user.isGuard && user.isActive
    ).toList();
  }

  // Hämta användare med ID
  User? getUserById(String id) {
    try {
      return _users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  // Hämta användare med användarnamn
  User? getUserByUsername(String username) {
    try {
      return _users.firstWhere((user) => user.username == username);
    } catch (e) {
      return null;
    }
  }

  // Lägg till användare (endast för admin)
  bool addUser(User user, String password) {
    if (!_authService.isAdmin) return false;

    // Kontrollera om användarnamnet redan finns
    if (_users.any((u) => u.username == user.username)) {
      return false;
    }

    _users.add(user);
    _passwords[user.username] = password;
    return true;
  }

  // Uppdatera användare (endast för admin)
  bool updateUser(User user) {
    if (!_authService.isAdmin) return false;

    final index = _users.indexWhere((u) => u.id == user.id);
    if (index != -1) {
      _users[index] = user;
      return true;
    }
    return false;
  }

  // Uppdatera lösenord (endast för admin eller egen användare)
  bool updatePassword(String userId, String newPassword) {
    if (!_authService.isAdmin && _authService.currentUser?.id != userId) {
      return false;
    }

    final user = getUserById(userId);
    if (user != null) {
      _passwords[user.username] = newPassword;
      return true;
    }
    return false;
  }

  // Tilldela larm till vakt
  Future<bool> assignAlarmToGuard(String alarmId, String guardId) async {
    final alarm = _alarmService.getAlarmById(alarmId);
    final guard = getUserById(guardId);

    if (alarm == null || guard == null || !guard.isGuard) {
      return false;
    }

    try {
      // Use the AlarmService to assign the alarm to the user
      await _alarmService.assignAlarmToUser(alarmId, guardId, guard.name);

      // Update the guard's list of handled alarms
      final updatedGuard = guard.copyWith(
        handledAlarmIds: List<String>.from(guard.handledAlarmIds)..add(alarmId),
        lastActive: DateTime.now(),
      );

      // Save changes to the guard
      updateUser(updatedGuard);

      return true;
    } catch (e) {
      // Handle error
      return false;
    }
  }

  // Hämta vakter som hanterar larm
  List<User> getGuardsHandlingAlarms() {
    return _users.where((user) =>
      user.isGuard && user.isActive && user.handledAlarmIds.isNotEmpty
    ).toList();
  }

  // Hämta larm som hanteras av en vakt
  List<Alarm> getAlarmsHandledByGuard(String guardId) {
    final guard = getUserById(guardId);
    if (guard == null) return [];

    // Get the latest alarm data from the AlarmService
    final alarms = guard.handledAlarmIds
        .map((id) => _alarmService.getAlarmById(id))
        .where((alarm) => alarm != null)
        .cast<Alarm>()
        .toList();

    // Sort alarms by timestamp (newest first)
    alarms.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return alarms;
  }

  // Sök användare
  List<User> searchUsers(String query) {
    if (query.isEmpty) return getAllUsers();

    final lowercaseQuery = query.toLowerCase();
    return _users.where((user) {
      return user.name.toLowerCase().contains(lowercaseQuery) ||
             user.username.toLowerCase().contains(lowercaseQuery) ||
             user.email.toLowerCase().contains(lowercaseQuery) ||
             user.department.toLowerCase().contains(lowercaseQuery) ||
             user.position.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Generera ett unikt ID
  String generateId() {
    return 'user-${DateTime.now().millisecondsSinceEpoch}${Random().nextInt(1000)}';
  }

  // Validera lösenord
  bool validatePassword(String username, String password) {
    return _passwords[username] == password;
  }
}
