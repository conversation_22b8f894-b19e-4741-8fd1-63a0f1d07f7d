import 'package:flutter/material.dart';
import '../services/iot_device_simulator.dart';

class AppDrawer extends StatelessWidget {
  final Function(int) onNavigate;
  final int currentIndex;

  const AppDrawer({
    super.key,
    required this.onNavigate,
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 20),
            decoration: const BoxDecoration(
              color: Colors.blue,
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircleAvatar(
                    radius: 35,
                    backgroundColor: Colors.white,
                    child: Icon(
                      Icons.person,
                      size: 45,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Paratus Vakt',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Användare',
                    style: TextStyle(
                      color: Colors.white.withAlpha(204), // 0.8 * 255 = 204
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.home,
                  title: 'Hem',
                  index: 0,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Icons.warning,
                  title: 'Larm',
                  index: 1,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Icons.note,
                  title: 'Rapporter',
                  index: 2,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Icons.bar_chart,
                  title: 'Statistik',
                  index: 3,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Icons.group,
                  title: 'Team',
                  index: 4,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Icons.task,
                  title: 'Uppgifter',
                  index: 5,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Icons.calendar_today,
                  title: 'Schema',
                  index: 6,
                  context: context,
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.settings),
                  title: const Text('Inställningar'),
                  onTap: () {
                    Navigator.pop(context); // Stäng drawer
                    // Visa inställningar (implementeras senare)
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Inställningar (kommer snart)')),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.alarm_add),
                  title: const Text('Simulera larm'),
                  onTap: () {
                    Navigator.pop(context); // Stäng drawer
                    IoTDeviceSimulator().triggerManualAlarm();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Larm simulerat')),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('Om appen'),
                  onTap: () {
                    Navigator.pop(context); // Stäng drawer
                    _showAboutDialog(context);
                  },
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            color: Colors.blue.withAlpha(25), // 0.1 * 255 = 25
            child: Row(
              children: [
                const Icon(Icons.info_outline, size: 20, color: Colors.blue),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    'App version 1.0.0',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required int index,
    required BuildContext context,
  }) {
    final isSelected = currentIndex == index;
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Colors.blue : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? Colors.blue : null,
        ),
      ),
      selected: isSelected,
      onTap: () {
        Navigator.pop(context); // Stäng drawer
        onNavigate(index);
      },
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Om Paratus Vakt'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Paratus Vakt är en app för vaktbolag som hanterar larm från IoT-enheter.'),
            SizedBox(height: 10),
            Text('Funktioner:'),
            Text('• Ta emot larm från IoT-enheter (gula och röda)'),
            Text('• Visa tydliga visuella och ljudbaserade notifikationer'),
            Text('• Hantera larm (markera som pågående/åtgärdade)'),
            Text('• Skapa rapporter om larm'),
            Text('• Visa statistik över larm'),
            Text('• Hantera team och schema'),
            SizedBox(height: 10),
            Text('Version: 1.0.0'),
            Text('© 2025 Paratus Vakt'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Stäng'),
          ),
        ],
      ),
    );
  }
}
