import '../models/alert.dart';

class AlertStatusUtils {
  static String statusToString(AlertStatus status) {
    switch (status) {
      case AlertStatus.active:
        return 'active';
      case AlertStatus.acknowledged:
        return 'acknowledged';
      case AlertStatus.resolved:
        return 'resolved';
      case AlertStatus.false_alarm:
        return 'false_alarm';
    }
  }

  static AlertStatus stringToStatus(String? status) {
    switch (status) {
      case 'active':
        return AlertStatus.active;
      case 'acknowledged':
        return AlertStatus.acknowledged;
      case 'resolved':
        return AlertStatus.resolved;
      case 'false_alarm':
        return AlertStatus.false_alarm;
      default:
        return AlertStatus.active;
    }
  }
}
