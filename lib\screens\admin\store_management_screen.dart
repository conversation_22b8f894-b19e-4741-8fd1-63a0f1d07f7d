import 'package:flutter/material.dart';
import '../../models/store.dart';
import '../../services/store_service.dart';
import 'store_detail_screen.dart';

class StoreManagementScreen extends StatefulWidget {
  const StoreManagementScreen({super.key});
  
  @override
  State<StoreManagementScreen> createState() => _StoreManagementScreenState();
}

class _StoreManagementScreenState extends State<StoreManagementScreen> {
  final StoreService _storeService = StoreService();
  final TextEditingController _searchController = TextEditingController();
  
  List<Store> _stores = [];
  List<Store> _filteredStores = [];
  
  @override
  void initState() {
    super.initState();
    _loadStores();
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  void _loadStores() {
    _stores = _storeService.getAllStores();
    _filteredStores = List.from(_stores);
    setState(() {});
  }
  
  void _filterStores(String query) {
    if (query.isEmpty) {
      _filteredStores = List.from(_stores);
    } else {
      _filteredStores = _storeService.searchStores(query);
    }
    setState(() {});
  }
  
  void _addNewStore() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StoreDetailScreen(isNewStore: true),
      ),
    ).then((_) {
      _loadStores();
    });
  }
  
  void _editStore(Store store) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoreDetailScreen(
          isNewStore: false,
          store: store,
        ),
      ),
    ).then((_) {
      _loadStores();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Butikshantering'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addNewStore,
            tooltip: 'Lägg till ny butik',
          ),
        ],
      ),
      body: Column(
        children: [
          // Sökfält
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Sök butiker',
                hintText: 'Ange namn, adress eller stad...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterStores('');
                        },
                      )
                    : null,
              ),
              onChanged: _filterStores,
            ),
          ),
          
          // Butikslista
          Expanded(
            child: _filteredStores.isEmpty
                ? const Center(
                    child: Text(
                      'Inga butiker hittades',
                      style: TextStyle(fontSize: 16),
                    ),
                  )
                : ListView.builder(
                    itemCount: _filteredStores.length,
                    itemBuilder: (context, index) {
                      final store = _filteredStores[index];
                      final devices = _storeService.getDevicesForStore(store.id);
                      
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          title: Text(
                            store.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(store.fullAddress),
                              Text(
                                'Enheter: ${devices.length}',
                                style: TextStyle(
                                  color: devices.isEmpty
                                      ? Colors.red
                                      : Colors.green,
                                ),
                              ),
                            ],
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _editStore(store),
                            tooltip: 'Redigera butik',
                          ),
                          onTap: () => _editStore(store),
                          isThreeLine: true,
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewStore,
        tooltip: 'Lägg till ny butik',
        child: const Icon(Icons.add),
      ),
    );
  }
}
