const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: `https://${serviceAccount.project_id}.firebaseio.com`
});

// User configurations for all three account types
// IMPORTANT: Change these credentials before using!
const users = [
  {
    email: '<EMAIL>', // CHANGE THIS TO YOUR DESIRED EMAIL
    password: 'YourSecurePassword123!', // CHANGE THIS TO YOUR DESIRED PASSWORD
    displayName: 'Admin User', // CHANGE THIS TO YOUR DESIRED NAME
    role: 1, // 1 = admin role
    roleName: 'admin',
    position: 'Administrator',
    department: 'Administration',
    stores: []
  },
  {
    email: '<EMAIL>', // CHANGE THIS TO YOUR DESIRED EMAIL
    password: 'YourSecurePassword123!', // <PERSON><PERSON><PERSON> THIS TO YOUR DESIRED PASSWORD
    displayName: 'Store User', // <PERSON>ANGE THIS TO YOUR DESIRED NAME
    role: 2, // 2 = customer/store role
    roleName: 'store',
    position: 'Store Manager',
    department: 'Retail',
    storeId: 'store-1' // You may want to change this to a real store ID
  },
  {
    email: '<EMAIL>', // CHANGE THIS TO YOUR DESIRED EMAIL
    password: 'YourSecurePassword123!', // CHANGE THIS TO YOUR DESIRED PASSWORD
    displayName: 'Guard User', // CHANGE THIS TO YOUR DESIRED NAME
    role: 0, // 0 = guard role
    roleName: 'guard',
    position: 'Security Guard',
    department: 'Security',
    stores: ['store-1'] // You may want to change this to real store IDs
  }
];

// Function to create a user
async function createUser(userConfig) {
  try {
    // Create the user in Firebase Authentication
    const userRecord = await admin.auth().createUser({
      email: userConfig.email,
      password: userConfig.password,
      displayName: userConfig.displayName,
    });

    console.log(`Successfully created user: ${userRecord.uid}`);

    // Set custom claims with role only - store assignments will be in Firestore
    const claims = { role: userConfig.roleName };

    await admin.auth().setCustomUserClaims(userRecord.uid, claims);
    console.log(`Successfully set ${userConfig.roleName} custom claims`);

    // Create user document in Firestore
    const userData = {
      username: userConfig.email.split('@')[0],
      name: userConfig.displayName,
      role: userConfig.role,
      email: userConfig.email,
      phoneNumber: '',
      status: 0, // 0 = active
      position: userConfig.position,
      department: userConfig.department,
      lastActive: admin.firestore.FieldValue.serverTimestamp(),
      handledAlarmIds: []
    };

    // Add role-specific fields
    if (userConfig.roleName === 'store' && userConfig.storeId) {
      userData.storeId = userConfig.storeId;
    } else {
      userData.stores = userConfig.stores || [];
    }

    console.log(`Attempting to create ${userConfig.roleName} document in Firestore...`);

    // Use the Firebase Admin SDK's Firestore functionality
    await admin.firestore().doc(`users/${userRecord.uid}`).set(userData);
    console.log(`Successfully created ${userConfig.roleName} document in Firestore`);

    console.log('');
    console.log(`You can now log in as ${userConfig.roleName} with:`);
    console.log(`Email: ${userConfig.email}`);
    console.log(`Password: ${userConfig.password}`);
    console.log('');

    return userRecord;
  } catch (error) {
    console.error(`Error creating ${userConfig.roleName} user:`, error);
    return null;
  }
}

// Create all users sequentially
async function createAllUsers() {
  try {
    for (const userConfig of users) {
      await createUser(userConfig);
    }
    console.log('All users created successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error creating users:', error);
    process.exit(1);
  }
}

// Start the process
createAllUsers();
