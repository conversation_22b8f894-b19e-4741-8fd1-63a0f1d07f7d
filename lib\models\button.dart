import 'package:cloud_firestore/cloud_firestore.dart';

enum ButtonStatus { active, inactive, service }

class Button {
  final String id;
  final String label;
  final String storeId;
  final ButtonStatus status;
  final DateTime createdAt;

  Button({
    required this.id,
    required this.label,
    required this.storeId,
    this.status = ButtonStatus.active,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  bool get isActive => status == ButtonStatus.active;

  Button copyWith({
    String? id,
    String? label,
    String? storeId,
    ButtonStatus? status,
    DateTime? createdAt,
  }) {
    return Button(
      id: id ?? this.id,
      label: label ?? this.label,
      storeId: storeId ?? this.storeId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'label': label,
      'storeId': storeId,
      'status': _statusToString(status),
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory Button.fromMap(Map<String, dynamic> map) {
    return Button(
      id: map['id'] ?? '',
      label: map['label'] ?? '',
      storeId: map['storeId'] ?? '',
      status: _stringToStatus(map['status']),
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
    );
  }

  static ButtonStatus _stringToStatus(String? status) {
    switch (status) {
      case 'active':
        return ButtonStatus.active;
      case 'inactive':
        return ButtonStatus.inactive;
      case 'service':
        return ButtonStatus.service;
      default:
        return ButtonStatus.active;
    }
  }

  static String _statusToString(ButtonStatus status) {
    switch (status) {
      case ButtonStatus.active:
        return 'active';
      case ButtonStatus.inactive:
        return 'inactive';
      case ButtonStatus.service:
        return 'service';
    }
  }
}
