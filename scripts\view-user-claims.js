const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

// Initialize Firebase Admin with your service account
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// Get user ID from command line arguments
const userId = process.argv[2];

if (!userId) {
  console.error('Please provide a user ID as an argument');
  console.log('Usage: node view-user-claims.js USER_ID');
  process.exit(1);
}

// Function to get and display user claims
async function getUserClaims(uid) {
  try {
    const userRecord = await admin.auth().getUser(uid);
    console.log('User details:');
    console.log('  UID:', userRecord.uid);
    console.log('  Email:', userRecord.email);
    console.log('  Display name:', userRecord.displayName || 'Not set');
    console.log('\nCustom claims:');
    console.log(JSON.stringify(userRecord.customClaims, null, 2) || 'No custom claims set');
  } catch (error) {
    console.error('Error fetching user:', error);
  } finally {
    process.exit(0);
  }
}

// Execute the function
getUserClaims(userId);
