import 'alarm.dart';

class AlarmReport {
  final String id;
  final String alarmId;
  final DateTime timestamp;
  final String content;
  final String authorName;

  AlarmReport({
    required this.id,
    required this.alarmId,
    required this.timestamp,
    required this.content,
    required this.authorName,
  });

  AlarmReport copyWith({
    String? id,
    String? alarmId,
    DateTime? timestamp,
    String? content,
    String? authorName,
  }) {
    return AlarmReport(
      id: id ?? this.id,
      alarmId: alarmId ?? this.alarmId,
      timestamp: timestamp ?? this.timestamp,
      content: content ?? this.content,
      authorName: authorName ?? this.authorName,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'alarmId': alarmId,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'content': content,
      'authorName': authorName,
    };
  }

  factory AlarmReport.fromMap(Map<String, dynamic> map) {
    return AlarmReport(
      id: map['id'],
      alarmId: map['alarmId'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      content: map['content'],
      authorName: map['authorName'],
    );
  }
}
