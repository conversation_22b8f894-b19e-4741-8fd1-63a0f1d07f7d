import 'package:flutter/material.dart';

enum ActivityType {
  alarm,
  order,
  user,
  device,
  store,
  system
}

enum ActivityAction {
  created,
  updated,
  deleted,
  approved,
  rejected,
  completed,
  cancelled,
  assigned,
  handled
}

enum ActivitySource {
  admin,
  guard,
  store,
  system
}

class ActivityLog {
  final String id;
  final DateTime timestamp;
  final ActivityType type;
  final ActivityAction action;
  final ActivitySource source;
  final String? userId;
  final String? userName;
  final String? targetId;
  final String? targetName;
  final String? description;
  
  const ActivityLog({
    required this.id,
    required this.timestamp,
    required this.type,
    required this.action,
    required this.source,
    this.userId,
    this.userName,
    this.targetId,
    this.targetName,
    this.description,
  });
  
  // Hjälpmetod för att få en ikon baserat på aktivitetstyp
  IconData get icon {
    switch (type) {
      case ActivityType.alarm:
        return Icons.warning;
      case ActivityType.order:
        return Icons.security;
      case ActivityType.user:
        return Icons.person;
      case ActivityType.device:
        return Icons.devices;
      case ActivityType.store:
        return Icons.store;
      case ActivityType.system:
        return Icons.settings;
    }
  }
  
  // Hjälpmetod för att få en färg baserat på aktivitetstyp
  Color getColor() {
    switch (type) {
      case ActivityType.alarm:
        return Colors.red;
      case ActivityType.order:
        return Colors.blue;
      case ActivityType.user:
        return Colors.purple;
      case ActivityType.device:
        return Colors.green;
      case ActivityType.store:
        return Colors.orange;
      case ActivityType.system:
        return Colors.grey;
    }
  }
  
  // Hjälpmetod för att få en beskrivande text
  String getDescription() {
    if (description != null) {
      return description!;
    }
    
    String actionText = '';
    switch (action) {
      case ActivityAction.created:
        actionText = 'skapade';
        break;
      case ActivityAction.updated:
        actionText = 'uppdaterade';
        break;
      case ActivityAction.deleted:
        actionText = 'raderade';
        break;
      case ActivityAction.approved:
        actionText = 'godkände';
        break;
      case ActivityAction.rejected:
        actionText = 'avvisade';
        break;
      case ActivityAction.completed:
        actionText = 'slutförde';
        break;
      case ActivityAction.cancelled:
        actionText = 'avbröt';
        break;
      case ActivityAction.assigned:
        actionText = 'tilldelade';
        break;
      case ActivityAction.handled:
        actionText = 'hanterade';
        break;
    }
    
    String typeText = '';
    switch (type) {
      case ActivityType.alarm:
        typeText = 'larm';
        break;
      case ActivityType.order:
        typeText = 'beställning';
        break;
      case ActivityType.user:
        typeText = 'användare';
        break;
      case ActivityType.device:
        typeText = 'enhet';
        break;
      case ActivityType.store:
        typeText = 'butik';
        break;
      case ActivityType.system:
        typeText = 'systeminställning';
        break;
    }
    
    String userText = userName ?? 'Någon';
    String targetText = targetName ?? '';
    
    if (targetText.isNotEmpty) {
      return '$userText $actionText $typeText: $targetText';
    } else {
      return '$userText $actionText $typeText';
    }
  }
  
  // Hjälpmetod för att få källtext
  String getSourceText() {
    switch (source) {
      case ActivitySource.admin:
        return 'Admin';
      case ActivitySource.guard:
        return 'Vakt';
      case ActivitySource.store:
        return 'Butik';
      case ActivitySource.system:
        return 'System';
    }
  }
}
