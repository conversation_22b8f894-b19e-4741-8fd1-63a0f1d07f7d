import 'package:flutter/material.dart';
import '../theme/security_theme.dart';

/// En professionell säkerhetslogo för appen
class SecurityLogo extends StatelessWidget {
  final double size;
  final bool showText;
  final bool isVertical;
  final Color? color;

  const SecurityLogo({
    super.key,
    this.size = 60,
    this.showText = true,
    this.isVertical = false,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final logoColor = color ?? SecurityTheme.primaryColor;
    final textColor = color ?? SecurityTheme.primaryTextColor;
    
    final logoWidget = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: logoColor,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Icon(
          Icons.shield,
          color: Colors.white,
          size: size * 0.6,
        ),
      ),
    );
    
    if (!showText) {
      return logoWidget;
    }
    
    final textWidget = Text(
      'PARATUS',
      style: TextStyle(
        color: textColor,
        fontSize: size * 0.4,
        fontWeight: FontWeight.bold,
        letterSpacing: 1.5,
      ),
    );
    
    if (isVertical) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          logoWidget,
          SizedBox(height: size * 0.2),
          textWidget,
        ],
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          logoWidget,
          SizedBox(width: size * 0.2),
          textWidget,
        ],
      );
    }
  }
}
