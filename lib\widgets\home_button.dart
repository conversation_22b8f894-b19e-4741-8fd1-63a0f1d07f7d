import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../screens/admin/admin_dashboard.dart';
import '../screens/guard/guard_dashboard.dart';
import '../screens/customer/customer_dashboard.dart';

/// En widget som lägger till en "Hem"-knapp i appbaren.
///
/// Denna knapp navigerar tillbaka till hemsidan (dashboard) genom att rensa
/// navigeringsstacken och skapa en ny instans av dashboard-skärmen.
class HomeButton extends StatelessWidget {
  const HomeButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.home),
      tooltip: 'Tillbaka till startsidan',
      onPressed: () => navigateToHome(context),
    );
  }

  /// Navigerar tillbaka till hemsidan (dashboard) genom att rensa navigeringsstacken.
  static void navigateToHome(BuildContext context) {
    final authService = AuthService();

    // Navigera till rätt dashboard baserat på användarroll
    Widget dashboard;
    if (authService.isAdmin) {
      dashboard = const AdminDashboard();
    } else if (authService.isGuard) {
      dashboard = const GuardDashboard();
    } else {
      dashboard = const CustomerDashboard();
    }

    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => dashboard),
      (route) => false, // Ta bort alla tidigare rutter från stacken
    );
  }
}
