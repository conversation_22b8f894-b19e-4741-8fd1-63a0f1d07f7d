#!/usr/bin/env node
const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const uid = process.argv[2];
if (!uid) {
  console.error('Please provide a user ID');
  process.exit(1);
}

admin.auth().getUser(uid)
  .then(user => {
    console.log(JSON.stringify(user.customClaims, null, 2));
    process.exit(0);
  })
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
