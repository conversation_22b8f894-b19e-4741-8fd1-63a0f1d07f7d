import 'dart:async';
import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../models/store.dart';
import '../../models/alarm.dart';
import '../../models/guard_order.dart';
import '../../services/auth_service.dart';
import '../../services/store_service.dart';
import '../../services/alarm_service.dart';
import '../../utils/responsive_layout.dart';
import '../../theme/security_theme.dart';
import '../../widgets/security_logo.dart';
import '../alarm_detail_screen.dart';
import 'customer_statistics_screen.dart';
import 'customer_reports_screen.dart';
import 'guard_orders_screen.dart';

class CustomerDashboard extends StatefulWidget {
  const CustomerDashboard({super.key});

  @override
  State<CustomerDashboard> createState() => _CustomerDashboardState();
}

class _CustomerDashboardState extends State<CustomerDashboard> {
  final AuthService _authService = AuthService();
  final StoreService _storeService = StoreService();
  final AlarmService _alarmService = AlarmService();

  User? _currentUser;
  Store? _store;
  List<Alarm> _storeAlarms = [];
  int _activeAlarms = 0;
  int _resolvedAlarms = 0;
  int _pendingOrders = 0;
  int _approvedOrders = 0;
  int _completedOrders = 0;
  int _cancelledOrders = 0;
  int _totalAlarms = 0;
  int _totalOrders = 0;
  StreamSubscription? _alarmSubscription;

  @override
  void initState() {
    super.initState();

    // Säkrare hantering av currentUser
    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      _currentUser = currentUser;
      _loadStoreData();

      // Lyssna på nya larm
      _alarmSubscription = _alarmService.alarmStream.listen((_) {
        if (mounted) {
          _loadStoreData();
        }
      });
    } else {
      // Hantera fallet när användaren inte är inloggad
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _authService.logout(); // Säkerställ att användaren loggas ut
      });
    }
  }

  @override
  void dispose() {
    // Avsluta prenumerationen när widgeten tas bort
    _alarmSubscription?.cancel();
    super.dispose();
  }

  void _loadStoreData() {
    if (_currentUser != null && _currentUser?.storeId != null) {
      final storeId = _currentUser!.storeId!;
      final store = _storeService.getStoreById(storeId);

      if (store != null) {
        final alarms = _alarmService.getAlarmsForStore(store.id);

        // Hämta beställningar för butiken
        final orders = _storeService.getGuardOrdersForStore(store.id);
        final pendingOrders = orders.where((o) => o.status == OrderStatus.pending).length;
        final approvedOrders = orders.where((o) => o.status == OrderStatus.approved).length;
        final completedOrders = orders.where((o) => o.status == OrderStatus.completed).length;
        final cancelledOrders = orders.where((o) => o.status == OrderStatus.cancelled).length;

        setState(() {
          _store = store;
          _storeAlarms = alarms;
          _activeAlarms = alarms.where((a) => !a.isHandled).length;
          _resolvedAlarms = alarms.where((a) => a.isHandled).length;
          _totalAlarms = alarms.length;

          _pendingOrders = pendingOrders;
          _approvedOrders = approvedOrders;
          _completedOrders = completedOrders;
          _cancelledOrders = cancelledOrders;
          _totalOrders = orders.length;
        });
      }
    }
  }

  void _logout() {
    showDialog(
      context: context,
      barrierDismissible: false, // Förhindra att användaren stänger dialogen genom att klicka utanför
      builder: (context) => AlertDialog(
        title: const Text('Logga ut'),
        content: const Text('Är du säker på att du vill logga ut?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Avbryt'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Visa laddningsindikator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );

              // Avsluta prenumerationen innan utloggning
              _alarmSubscription?.cancel();
              _alarmSubscription = null;

              // Stäng dialogerna
              Navigator.pop(context); // Stäng laddningsindikatorn
              Navigator.pop(context); // Stäng bekräftelsedialogen

              // Logga ut användaren
              await _authService.logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logga ut'),
          ),
        ],
      ),
    );
  }

  void _navigateToScreen(Widget screen) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    ).then((_) => _loadStoreData());
  }

  @override
  Widget build(BuildContext context) {
    if (_store == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Butikspanel'),
          actions: [
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: _logout,
              tooltip: 'Logga ut',
            ),
          ],
        ),
        body: const Center(
          child: Text('Ingen butik hittades för denna användare'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const SecurityLogo(
              size: 36,
              showText: false,
            ),
            const SizedBox(width: 12),
            Text(_store!.name),
          ],
        ),
        actions: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.bar_chart),
              onPressed: () {
                Scaffold.of(context).openEndDrawer();
              },
              tooltip: 'Visa statistik',
            ),
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'Logga ut',
          ),
        ],
        elevation: 2,
        shadowColor: Colors.black.withAlpha(50),
      ),
      endDrawer: _buildStatisticsDrawer(),
      body: SingleChildScrollView(
        padding: ResponsiveLayout.getAdaptivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Välkomstsektion
            Container(
              decoration: BoxDecoration(
                gradient: SecurityTheme.primaryGradient,
                borderRadius: SecurityTheme.roundedBorder,
                boxShadow: SecurityTheme.cardShadow,
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(50),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.store,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Välkommen, ${_store!.name}',
                            style: TextStyle(
                              fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 20),
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            _store!.fullAddress,
                            style: TextStyle(
                              fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 16),
                              color: Colors.white.withAlpha(200),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Snabbåtkomst
            Text(
              'Snabbåtkomst',
              style: TextStyle(
                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Statistik',
                    Icons.bar_chart,
                    SecurityTheme.infoColor,
                    () => _navigateToScreen(CustomerStatisticsScreen(storeId: _store!.id, storeName: _store!.name)),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildActionCard(
                    'Rapporter',
                    Icons.note,
                    SecurityTheme.secondaryColor,
                    () => _navigateToScreen(CustomerReportsScreen(storeId: _store!.id)),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Beställ vakter',
                    Icons.security,
                    SecurityTheme.successColor,
                    () => _navigateToScreen(GuardOrdersScreen(storeId: _store!.id)),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Builder(
                    builder: (context) => _buildActionCard(
                      'Översikt',
                      Icons.dashboard,
                      SecurityTheme.warningColor,
                      () {
                        // Öppna statistik-drawer
                        Scaffold.of(context).openEndDrawer();
                      },
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Senaste larm
            Text(
              'Senaste larm',
              style: TextStyle(
                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildRecentAlarms(),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Container(
      height: 120, // Fast höjd för alla kort
      decoration: BoxDecoration(
        borderRadius: SecurityTheme.roundedBorder,
        boxShadow: SecurityTheme.cardShadow,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color,
            Color.lerp(color, Colors.black, 0.2) ?? color,
          ],
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: SecurityTheme.roundedBorder,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: ResponsiveLayout.getAdaptiveIconSize(context, 32),
                  color: Colors.white,
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 14),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecentAlarms() {
    if (_storeAlarms.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: Text('Inga larm har registrerats för denna butik'),
          ),
        ),
      );
    }

    // Sortera larm efter datum (nyaste först)
    final sortedAlarms = List<Alarm>.from(_storeAlarms)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Visa de 5 senaste larmen
    final recentAlarms = sortedAlarms.take(5).toList();

    return Card(
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: recentAlarms.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final alarm = recentAlarms[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: alarm.type == AlarmType.red
                  ? SecurityTheme.dangerColor.withAlpha(30)
                  : SecurityTheme.warningColor.withAlpha(30),
              child: Icon(
                Icons.warning,
                color: alarm.type == AlarmType.red ? SecurityTheme.dangerColor : SecurityTheme.warningColor,
              ),
            ),
            title: Text(
              alarm.type == AlarmType.red ? 'AKUT LARM' : 'VARNINGSLARM',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: alarm.type == AlarmType.red ? SecurityTheme.dangerColor : SecurityTheme.warningColor,
              ),
            ),
            subtitle: Text(
              '${alarm.location} - ${_formatDateTime(alarm.timestamp)}',
              style: TextStyle(color: SecurityTheme.secondaryTextColor),
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: alarm.status == AlarmStatus.new_
                    ? SecurityTheme.dangerColor
                    : alarm.status == AlarmStatus.ongoing
                        ? SecurityTheme.infoColor
                        : SecurityTheme.successColor,
                borderRadius: BorderRadius.circular(SecurityTheme.borderRadius / 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(40),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                alarm.status == AlarmStatus.new_
                    ? 'NYTT'
                    : alarm.status == AlarmStatus.ongoing
                        ? 'PÅGÅENDE'
                        : 'ÅTGÄRDAT',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AlarmDetailScreen(alarmId: alarm.id),
                ),
              );
            },
          );
        },
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildStatisticsDrawer() {
    return Drawer(
      width: MediaQuery.of(context).size.width * 0.85,
      child: Container(
        decoration: BoxDecoration(
          color: SecurityTheme.backgroundColor,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Drawer header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: SecurityTheme.primaryGradient,
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.cardShadow,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.bar_chart,
                              color: Colors.white,
                              size: ResponsiveLayout.getAdaptiveIconSize(context, 24),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Statistik',
                              style: TextStyle(
                                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 20),
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.white),
                          onPressed: () => Navigator.pop(context),
                          tooltip: 'Stäng',
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Knapp för detaljerad statistik
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.buttonShadow,
                    ),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context); // Stäng drawer först
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CustomerStatisticsScreen(
                              storeId: _store!.id,
                              storeName: _store!.name,
                            ),
                          ),
                        ).then((_) => _loadStoreData());
                      },
                      icon: const Icon(Icons.analytics),
                      label: const Text('Visa detaljerad statistik'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Larmstatistik
                  Text(
                    'Larmstatistik',
                    style: TextStyle(
                      fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                      fontWeight: FontWeight.bold,
                      color: SecurityTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Larmstatistik kort
                  Container(
                    decoration: BoxDecoration(
                      color: SecurityTheme.cardColor,
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.cardShadow,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          _buildStatRow(
                            'Aktiva larm',
                            _activeAlarms.toString(),
                            Icons.warning,
                            SecurityTheme.dangerColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Åtgärdade larm',
                            _resolvedAlarms.toString(),
                            Icons.check_circle,
                            SecurityTheme.successColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Totalt antal larm',
                            _totalAlarms.toString(),
                            Icons.analytics,
                            SecurityTheme.infoColor,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Beställningsstatistik
                  Text(
                    'Beställningsstatistik',
                    style: TextStyle(
                      fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                      fontWeight: FontWeight.bold,
                      color: SecurityTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Beställningsstatistik kort
                  Container(
                    decoration: BoxDecoration(
                      color: SecurityTheme.cardColor,
                      borderRadius: SecurityTheme.roundedBorder,
                      boxShadow: SecurityTheme.cardShadow,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          _buildStatRow(
                            'Väntande',
                            _pendingOrders.toString(),
                            Icons.pending_actions,
                            SecurityTheme.warningColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Godkända',
                            _approvedOrders.toString(),
                            Icons.check_circle_outline,
                            SecurityTheme.successColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Genomförda',
                            _completedOrders.toString(),
                            Icons.task_alt,
                            SecurityTheme.infoColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Avbrutna',
                            _cancelledOrders.toString(),
                            Icons.cancel,
                            SecurityTheme.dangerColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Totalt antal',
                            _totalOrders.toString(),
                            Icons.analytics,
                            SecurityTheme.secondaryColor,
                          ),
                          const Divider(),
                          _buildStatRow(
                            'Enheter',
                            _store!.deviceIds.length.toString(),
                            Icons.devices,
                            SecurityTheme.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String title, String value, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: color.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: SecurityTheme.primaryTextColor,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: color.withAlpha(20),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
