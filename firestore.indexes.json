{"indexes": [{"collectionGroup": "alerts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "alerts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "pressedAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "buttons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "requestedAt", "order": "DESCENDING"}]}, {"collectionGroup": "shifts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "guardId", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "DESCENDING"}]}, {"collectionGroup": "reports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "storeId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}