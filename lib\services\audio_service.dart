import 'package:flutter/services.dart';
import '../models/alarm.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  
  factory AudioService() {
    return _instance;
  }
  
  AudioService._internal();
  
  // I en riktig app skulle vi använda en ljudbibliotekspaket som audioplayers
  // Men för denna demo använder vi bara systemljud
  Future<void> playAlarmSound(AlarmType alarmType) async {
    // Spela upp systemljud baserat på larmtyp
    switch (alarmType) {
      case AlarmType.red:
        await HapticFeedback.heavyImpact();
        await Future.delayed(const Duration(milliseconds: 200));
        await HapticFeedback.heavyImpact();
        await Future.delayed(const Duration(milliseconds: 200));
        await HapticFeedback.heavyImpact();
        break;
      case AlarmType.yellow:
        await HapticFeedback.mediumImpact();
        await Future.delayed(const Duration(milliseconds: 300));
        await HapticFeedback.mediumImpact();
        break;
    }
    
    // Spela systemljud
    await SystemSound.play(SystemSoundType.alert);
  }
  
  // Stoppa ljud (skulle implementeras med ett riktigt ljudbibliotek)
  void stopAlarmSound() {
    // Implementera med ett riktigt ljudbibliotek
  }
}
