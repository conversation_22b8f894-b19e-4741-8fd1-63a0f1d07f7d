import 'package:flutter/material.dart';
import '../../models/alarm.dart';
import '../../models/guard_order.dart';
import '../../services/alarm_service.dart';
import '../../services/store_service.dart';
import '../../utils/responsive_layout.dart';
import '../../widgets/home_button.dart';

class CustomerStatisticsScreen extends StatefulWidget {
  final String storeId;
  final String? storeName;

  const CustomerStatisticsScreen({
    super.key,
    required this.storeId,
    this.storeName,
  });

  @override
  State<CustomerStatisticsScreen> createState() => _CustomerStatisticsScreenState();
}

class _CustomerStatisticsScreenState extends State<CustomerStatisticsScreen> {
  final AlarmService _alarmService = AlarmService();
  final StoreService _storeService = StoreService();

  List<Alarm> _storeAlarms = [];
  List<GuardOrder> _storeOrders = [];
  String _selectedPeriod = 'month';
  String _selectedStatType = 'alarms'; // 'alarms' eller 'orders'
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    setState(() {
      _isLoading = true;
    });

    try {
      // Ladda både larm och beställningar
      _storeAlarms = _alarmService.getAlarmsForStore(widget.storeId);
      _storeOrders = _storeService.getGuardOrdersForStore(widget.storeId);

      debugPrint('Laddade ${_storeAlarms.length} larm och ${_storeOrders.length} beställningar');
    } catch (e) {
      debugPrint('Fel vid laddning av data: $e');
      _storeAlarms = [];
      _storeOrders = [];
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Alarm> _getFilteredAlarms() {
    final now = DateTime.now();
    DateTime startDate;

    switch (_selectedPeriod) {
      case 'week':
        startDate = DateTime(now.year, now.month, now.day - 7);
        break;
      case 'month':
        startDate = DateTime(now.year, now.month - 1, now.day);
        break;
      case 'year':
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      default:
        startDate = DateTime(now.year, now.month - 1, now.day);
    }

    return _storeAlarms.where((alarm) => alarm.timestamp.isAfter(startDate)).toList();
  }

  Map<String, int> _getAlarmsByType() {
    final filteredAlarms = _getFilteredAlarms();
    final redAlarms = filteredAlarms.where((a) => a.type == AlarmType.red).length;
    final yellowAlarms = filteredAlarms.where((a) => a.type == AlarmType.yellow).length;

    return {
      'Akuta larm': redAlarms,
      'Varningslarm': yellowAlarms,
    };
  }

  Map<String, int> _getAlarmsByStatus() {
    final filteredAlarms = _getFilteredAlarms();
    final newAlarms = filteredAlarms.where((a) => a.status == AlarmStatus.new_).length;
    final ongoingAlarms = filteredAlarms.where((a) => a.status == AlarmStatus.ongoing).length;
    final resolvedAlarms = filteredAlarms.where((a) => a.status == AlarmStatus.resolved).length;

    return {
      'Nya': newAlarms,
      'Pågående': ongoingAlarms,
      'Åtgärdade': resolvedAlarms,
    };
  }

  Map<String, int> _getAlarmsByDevice() {
    final filteredAlarms = _getFilteredAlarms();
    final result = <String, int>{};

    for (final alarm in filteredAlarms) {
      final device = _storeService.getDeviceById(alarm.deviceId);
      final deviceName = device?.name ?? alarm.deviceId;

      if (result.containsKey(deviceName)) {
        result[deviceName] = result[deviceName]! + 1;
      } else {
        result[deviceName] = 1;
      }
    }

    return result;
  }

  Map<String, int> _getAlarmsByDay() {
    final filteredAlarms = _getFilteredAlarms();
    final result = <String, int>{};

    final weekdays = [
      'Måndag',
      'Tisdag',
      'Onsdag',
      'Torsdag',
      'Fredag',
      'Lördag',
      'Söndag',
    ];

    // Initiera alla veckodagar med 0
    for (final day in weekdays) {
      result[day] = 0;
    }

    for (final alarm in filteredAlarms) {
      final weekday = weekdays[alarm.timestamp.weekday - 1];
      result[weekday] = result[weekday]! + 1;
    }

    return result;
  }

  // Metoder för beställningsstatistik
  List<GuardOrder> _getFilteredOrders() {
    final now = DateTime.now();
    DateTime startDate;

    switch (_selectedPeriod) {
      case 'week':
        startDate = DateTime(now.year, now.month, now.day - 7);
        break;
      case 'month':
        startDate = DateTime(now.year, now.month - 1, now.day);
        break;
      case 'year':
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      default:
        startDate = DateTime(now.year, now.month - 1, now.day);
    }

    return _storeOrders.where((order) => order.createdAt.isAfter(startDate)).toList();
  }

  Map<String, int> _getOrdersByStatus() {
    final filteredOrders = _getFilteredOrders();
    final pendingOrders = filteredOrders.where((o) => o.status == OrderStatus.pending).length;
    final approvedOrders = filteredOrders.where((o) => o.status == OrderStatus.approved).length;
    final completedOrders = filteredOrders.where((o) => o.status == OrderStatus.completed).length;
    final cancelledOrders = filteredOrders.where((o) => o.status == OrderStatus.cancelled).length;

    return {
      'Väntande': pendingOrders,
      'Godkända': approvedOrders,
      'Genomförda': completedOrders,
      'Avbrutna': cancelledOrders,
    };
  }

  Map<String, int> _getOrdersByType() {
    final filteredOrders = _getFilteredOrders();
    final entranceHostOrders = filteredOrders.where((o) => o.guardType == GuardType.entranceHost).length;
    final securityGuardOrders = filteredOrders.where((o) => o.guardType == GuardType.securityGuard).length;
    final guardOrders = filteredOrders.where((o) => o.guardType == GuardType.guard).length;

    return {
      'Entrévärd': entranceHostOrders,
      'Ordningsvakt': securityGuardOrders,
      'Väktare': guardOrders,
    };
  }

  Map<String, int> _getOrdersByDay() {
    final filteredOrders = _getFilteredOrders();
    final result = <String, int>{};

    final weekdays = [
      'Måndag',
      'Tisdag',
      'Onsdag',
      'Torsdag',
      'Fredag',
      'Lördag',
      'Söndag',
    ];

    // Initiera alla veckodagar med 0
    for (final day in weekdays) {
      result[day] = 0;
    }

    for (final order in filteredOrders) {
      final weekday = weekdays[order.createdAt.weekday - 1];
      result[weekday] = result[weekday]! + 1;
    }

    return result;
  }

  @override
  Widget build(BuildContext context) {
    final store = _storeService.getStoreById(widget.storeId);
    final storeName = widget.storeName ?? store?.name ?? 'Okänd butik';

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Statistik - $storeName'),
          actions: const [
            HomeButton(),
          ],
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Kontrollera om det finns några larm eller beställningar
    if (_storeAlarms.isEmpty && _storeOrders.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Statistik - $storeName'),
          actions: const [
            HomeButton(),
          ],
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.info_outline,
                size: 64,
                color: Colors.blue,
              ),
              const SizedBox(height: 16),
              const Text(
                'Ingen statistik att visa',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Det finns inga registrerade larm eller beställningar för ${store?.name ?? 'denna butik'}.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh),
                label: const Text('Uppdatera'),
              ),
            ],
          ),
        ),
      );
    }

    // Kontrollera om det finns larm men inga beställningar när beställningsstatistik är vald
    if (_selectedStatType == 'orders' && _storeOrders.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Statistik - $storeName'),
          actions: const [
            HomeButton(),
          ],
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.security_outlined,
                size: 64,
                color: Colors.orange,
              ),
              const SizedBox(height: 16),
              const Text(
                'Inga beställningar att visa',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Det finns inga registrerade beställningar för ${store?.name ?? 'denna butik'}.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedStatType = 'alarms';
                  });
                },
                icon: const Icon(Icons.warning),
                label: const Text('Visa larmstatistik istället'),
              ),
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh),
                label: const Text('Uppdatera'),
              ),
            ],
          ),
        ),
      );
    }

    // Kontrollera om det finns beställningar men inga larm när larmstatistik är vald
    if (_selectedStatType == 'alarms' && _storeAlarms.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Statistik - $storeName'),
          actions: const [
            HomeButton(),
          ],
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.warning_outlined,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Inga larm att visa',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Det finns inga registrerade larm för ${store?.name ?? 'denna butik'}.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedStatType = 'orders';
                  });
                },
                icon: const Icon(Icons.security),
                label: const Text('Visa beställningsstatistik istället'),
              ),
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh),
                label: const Text('Uppdatera'),
              ),
            ],
          ),
        ),
      );
    }

    // Hämta statistik för både larm och beställningar
    final filteredAlarms = _getFilteredAlarms();
    final filteredOrders = _getFilteredOrders();

    // Larmstatistik
    final alarmsByType = _getAlarmsByType();
    final alarmsByStatus = _getAlarmsByStatus();
    final alarmsByDevice = _getAlarmsByDevice();
    final alarmsByDay = _getAlarmsByDay();

    // Beställningsstatistik
    final ordersByStatus = _getOrdersByStatus();
    final ordersByType = _getOrdersByType();
    final ordersByDay = _getOrdersByDay();

    return Scaffold(
      appBar: AppBar(
        title: Text('Statistik - $storeName'),
        actions: const [
          HomeButton(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Välj statistiktyp och period
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Välj statistiktyp',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SegmentedButton<String>(
                      segments: const [
                        ButtonSegment(
                          value: 'alarms',
                          label: Text('Larm'),
                          icon: Icon(Icons.warning),
                        ),
                        ButtonSegment(
                          value: 'orders',
                          label: Text('Beställningar'),
                          icon: Icon(Icons.security),
                        ),
                      ],
                      selected: {_selectedStatType},
                      onSelectionChanged: (Set<String> newSelection) {
                        setState(() {
                          _selectedStatType = newSelection.first;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Välj period',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SegmentedButton<String>(
                      segments: const [
                        ButtonSegment(
                          value: 'week',
                          label: Text('Vecka'),
                          icon: Icon(Icons.calendar_view_week),
                        ),
                        ButtonSegment(
                          value: 'month',
                          label: Text('Månad'),
                          icon: Icon(Icons.calendar_view_month),
                        ),
                        ButtonSegment(
                          value: 'year',
                          label: Text('År'),
                          icon: Icon(Icons.calendar_today),
                        ),
                      ],
                      selected: {_selectedPeriod},
                      onSelectionChanged: (Set<String> newSelection) {
                        setState(() {
                          _selectedPeriod = newSelection.first;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Översikt
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedStatType == 'alarms' ? 'Larmöversikt' : 'Beställningsöversikt',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_selectedStatType == 'alarms') ...[
                      // Visa larmstatistik
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatItem(
                            'Totalt antal larm',
                            filteredAlarms.length.toString(),
                            Icons.warning,
                            Colors.blue,
                          ),
                          _buildStatItem(
                            'Akuta larm',
                            alarmsByType['Akuta larm'].toString(),
                            Icons.warning,
                            Colors.red,
                          ),
                          _buildStatItem(
                            'Varningslarm',
                            alarmsByType['Varningslarm'].toString(),
                            Icons.warning,
                            Colors.orange,
                          ),
                        ],
                      ),
                    ] else ...[
                      // Visa beställningsstatistik
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatItem(
                            'Totalt antal beställningar',
                            filteredOrders.length.toString(),
                            Icons.security,
                            Colors.blue,
                          ),
                          _buildStatItem(
                            'Väntande',
                            ordersByStatus['Väntande'].toString(),
                            Icons.pending_actions,
                            Colors.orange,
                          ),
                          _buildStatItem(
                            'Godkända',
                            ordersByStatus['Godkända'].toString(),
                            Icons.check_circle,
                            Colors.green,
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatItem(
                            'Genomförda',
                            ordersByStatus['Genomförda'].toString(),
                            Icons.task_alt,
                            Colors.blue,
                          ),
                          _buildStatItem(
                            'Avbrutna',
                            ordersByStatus['Avbrutna'].toString(),
                            Icons.cancel,
                            Colors.red,
                          ),
                          const SizedBox(width: 80), // För att balansera layouten
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Detaljerad statistik
            if (_selectedStatType == 'alarms') ...[
              // Larm per status
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Larm per status',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatItem(
                            'Nya',
                            alarmsByStatus['Nya'].toString(),
                            Icons.new_releases,
                            Colors.red,
                          ),
                          _buildStatItem(
                            'Pågående',
                            alarmsByStatus['Pågående'].toString(),
                            Icons.hourglass_bottom,
                            Colors.blue,
                          ),
                          _buildStatItem(
                            'Åtgärdade',
                            alarmsByStatus['Åtgärdade'].toString(),
                            Icons.check_circle,
                            Colors.green,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Larm per enhet
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Larm per enhet',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ...alarmsByDevice.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 3,
                                child: Text(entry.key),
                              ),
                              Expanded(
                                flex: 7,
                                child: LinearProgressIndicator(
                                  value: entry.value / (filteredAlarms.isEmpty ? 1 : filteredAlarms.length),
                                  backgroundColor: Colors.grey.shade200,
                                  color: Colors.blue,
                                  minHeight: 10,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                entry.value.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Larm per veckodag
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Larm per veckodag',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ...alarmsByDay.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 3,
                                child: Text(entry.key),
                              ),
                              Expanded(
                                flex: 7,
                                child: LinearProgressIndicator(
                                  value: entry.value / (filteredAlarms.isEmpty ? 1 : filteredAlarms.length),
                                  backgroundColor: Colors.grey.shade200,
                                  color: Colors.purple,
                                  minHeight: 10,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                entry.value.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ] else ...[
              // Beställningar per typ
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Beställningar per typ',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatItem(
                            'Entrévärd',
                            ordersByType['Entrévärd'].toString(),
                            Icons.person,
                            Colors.blue,
                          ),
                          _buildStatItem(
                            'Ordningsvakt',
                            ordersByType['Ordningsvakt'].toString(),
                            Icons.security,
                            Colors.red,
                          ),
                          _buildStatItem(
                            'Väktare',
                            ordersByType['Väktare'].toString(),
                            Icons.shield,
                            Colors.green,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Beställningar per veckodag
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Beställningar per veckodag',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ...ordersByDay.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 3,
                                child: Text(entry.key),
                              ),
                              Expanded(
                                flex: 7,
                                child: LinearProgressIndicator(
                                  value: entry.value / (filteredOrders.isEmpty ? 1 : filteredOrders.length),
                                  backgroundColor: Colors.grey.shade200,
                                  color: Colors.teal,
                                  minHeight: 10,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                entry.value.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Feedback från admin
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Feedback från admin',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (filteredOrders.where((o) => o.adminFeedback != null && o.adminFeedback!.isNotEmpty).isEmpty)
                        const Text('Ingen feedback har lämnats på dina beställningar ännu.')
                      else
                        ...filteredOrders
                            .where((o) => o.adminFeedback != null && o.adminFeedback!.isNotEmpty)
                            .take(5)
                            .map((order) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: order.statusColor,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        order.statusText,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Beställning ${order.id.substring(0, 4)}...',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  order.adminFeedback!,
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const Divider(),
                              ],
                            ),
                          );
                        }),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
