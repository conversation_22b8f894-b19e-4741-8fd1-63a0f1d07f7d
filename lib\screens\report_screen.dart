import 'package:flutter/material.dart';
import '../models/alarm_report.dart';
import '../services/alarm_service.dart';
import '../widgets/home_button.dart';
import 'alarm_detail_screen.dart';

class ReportScreen extends StatefulWidget {
  const ReportScreen({super.key});

  @override
  State<ReportScreen> createState() => _ReportScreenState();
}

class _ReportScreenState extends State<ReportScreen> {
  final AlarmService _alarmService = AlarmService();

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _viewAlarmDetails(String alarmId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AlarmDetailScreen(alarmId: alarmId),
      ),
    ).then((_) {
      // Uppdatera listan när vi kommer tillbaka från detaljvyn
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final reports = _alarmService.reports;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Rapporter'),
        actions: const [
          HomeButton(),
        ],
      ),
      body: reports.isEmpty
          ? const Center(child: Text('Inga rapporter har skapats'))
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: reports.length,
              itemBuilder: (context, index) {
                final report = reports[reports.length - 1 - index]; // Visa nyaste först
                final alarm = _alarmService.getAlarmById(report.alarmId);

                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Rapport: ${_formatDateTime(report.timestamp)}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            if (alarm != null)
                              TextButton(
                                onPressed: () => _viewAlarmDetails(alarm.id),
                                child: const Text('Visa larm'),
                              ),
                          ],
                        ),
                        const Divider(),
                        if (alarm != null) ...[
                          Text('Larm från: ${alarm.deviceId}'),
                          Text('Plats: ${alarm.location}'),
                          Text('Larmtid: ${_formatDateTime(alarm.timestamp)}'),
                          const SizedBox(height: 8),
                        ],
                        const Text(
                          'Rapport:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(report.content),
                        const SizedBox(height: 8),
                        Text(
                          'Skriven av: ${report.authorName}',
                          style: const TextStyle(fontStyle: FontStyle.italic),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }
}
