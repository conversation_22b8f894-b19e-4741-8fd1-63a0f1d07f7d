import 'dart:async';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/alarm.dart';
import '../models/alarm_report.dart';
import '../models/alert.dart';
import '../services/store_service.dart';
import '../services/database_service.dart';
import 'package:flutter/foundation.dart' show kDebugMode;

class AlarmService {
  static final AlarmService _instance = AlarmService._internal();
  final DatabaseService _databaseService = DatabaseService();

  factory AlarmService() {
    return _instance;
  }

  AlarmService._internal() {
    // Initiera streamcontrollern
    _alarmStreamController = StreamController<Alarm>.broadcast();

    // Start listening to alerts from Firestore
    _setupAlertsListener();
  }

  final List<Alarm> _alarms = [];
  final List<AlarmReport> _reports = [];
  // Använd late för att kunna återskapa streamcontrollern vid behov
  late StreamController<Alarm> _alarmStreamController;

  // <PERSON>ga för att indikera om tjänsten är aktiv
  bool _isActive = true;

  Stream<Alarm> get alarmStream => _alarmStreamController.stream;

  List<Alarm> get alarms => List.unmodifiable(_alarms);
  List<AlarmReport> get reports => List.unmodifiable(_reports);

  // Hämta alla larm
  List<Alarm> getAllAlarms() {
    return List.unmodifiable(_alarms);
  }

  // Getter för att kontrollera om tjänsten är aktiv
  bool get isActive => _isActive;

  void receiveAlarm(Alarm alarm) {
    // Kontrollera om tjänsten är aktiv innan larm läggs till
    if (_isActive) {
      _alarms.add(alarm);

      // Kontrollera om streamcontrollern är öppen innan vi skickar larm
      if (!_alarmStreamController.isClosed) {
        _alarmStreamController.add(alarm);
      }
    }
  }

  void updateAlarm(Alarm alarm) {
    final index = _alarms.indexWhere((a) => a.id == alarm.id);
    if (index != -1) {
      _alarms[index] = alarm;
    }
  }

  Future<void> updateAlarmStatus(String alarmId, AlarmStatus status, {String? userId, String? userName, String? note}) async {
    final index = _alarms.indexWhere((alarm) => alarm.id == alarmId);
    if (index != -1) {
      final alarm = _alarms[index];

      // Skapa en statusändring
      final statusChange = AlarmStatusChange(
        timestamp: DateTime.now(),
        fromStatus: alarm.status,
        toStatus: status,
        userId: userId,
        userName: userName,
        note: note,
      );

      // Lägg till statusändringen i historiken
      final newHistory = List<AlarmStatusChange>.from(alarm.statusHistory)
        ..add(statusChange);

      // Uppdatera larmet lokalt
      final updatedAlarm = alarm.copyWith(
        status: status,
        statusHistory: newHistory,
      );

      _alarms[index] = updatedAlarm;

      // Convert AlarmStatus to AlertStatus
      AlertStatus alertStatus;
      switch (status) {
        case AlarmStatus.new_:
          alertStatus = AlertStatus.active;
          break;
        case AlarmStatus.ongoing:
          alertStatus = AlertStatus.acknowledged;
          break;
        case AlarmStatus.resolved:
          alertStatus = AlertStatus.resolved;
          break;
      }

      // Update in Firestore
      try {
        await _databaseService.updateAlert(alarmId, {
          'status': _alertStatusToString(alertStatus),
          'statusHistory': updatedAlarm.statusHistory.map((change) {
            // Convert AlarmStatusChange to AlertStatusChange format
            return {
              'timestamp': Timestamp.fromDate(change.timestamp),
              'fromStatus': _convertAlarmStatusToAlertStatusString(change.fromStatus),
              'toStatus': _convertAlarmStatusToAlertStatusString(change.toStatus),
              'userId': change.userId,
              'userName': change.userName,
              'note': change.note,
            };
          }).toList(),
          if (status == AlarmStatus.resolved) 'resolvedAt': Timestamp.fromDate(DateTime.now()),
        });
      } catch (e) {
        if (kDebugMode) {
          print('Error updating alert status: $e');
        }
      }
    }
  }

  // Helper method to convert AlarmStatus to AlertStatus string
  String _convertAlarmStatusToAlertStatusString(AlarmStatus status) {
    switch (status) {
      case AlarmStatus.new_:
        return 'active';
      case AlarmStatus.ongoing:
        return 'acknowledged';
      case AlarmStatus.resolved:
        return 'resolved';
    }
  }

  // Helper method to convert AlertStatus to string
  String _alertStatusToString(AlertStatus status) {
    switch (status) {
      case AlertStatus.active:
        return 'active';
      case AlertStatus.acknowledged:
        return 'acknowledged';
      case AlertStatus.resolved:
        return 'resolved';
      case AlertStatus.false_alarm:
        return 'false_alarm';
    }
  }

  Future<void> markAlarmAsHandled(String alarmId, {String? userId, String? userName}) async {
    await updateAlarmStatus(
      alarmId,
      AlarmStatus.resolved,
      userId: userId,
      userName: userName,
      note: 'Markerat som åtgärdat',
    );
  }

  Future<void> markAlarmAsOngoing(String alarmId, {String? userId, String? userName}) async {
    await updateAlarmStatus(
      alarmId,
      AlarmStatus.ongoing,
      userId: userId,
      userName: userName,
      note: 'Markerat som pågående',
    );
  }

  Future<void> assignAlarmToUser(String alarmId, String userId, String userName) async {
    final index = _alarms.indexWhere((alarm) => alarm.id == alarmId);
    if (index != -1) {
      final alarm = _alarms[index];

      // Skapa en statusändring
      final statusChange = AlarmStatusChange(
        timestamp: DateTime.now(),
        fromStatus: alarm.status,
        toStatus: alarm.status, // Status ändras inte, bara tilldelning
        userId: userId,
        userName: userName,
        note: 'Tilldelad till $userName',
      );

      // Lägg till statusändringen i historiken
      final newHistory = List<AlarmStatusChange>.from(alarm.statusHistory)
        ..add(statusChange);

      // Uppdatera larmet lokalt
      final updatedAlarm = alarm.copyWith(
        assignedUserId: userId,
        assignedUserName: userName,
        statusHistory: newHistory,
      );

      _alarms[index] = updatedAlarm;

      // Update in Firestore
      try {
        await _databaseService.updateAlert(alarmId, {
          'handledBy': userId,
          'handlerName': userName,
          'statusHistory': updatedAlarm.statusHistory.map((change) {
            // Convert AlarmStatusChange to AlertStatusChange format
            return {
              'timestamp': Timestamp.fromDate(change.timestamp),
              'fromStatus': _convertAlarmStatusToAlertStatusString(change.fromStatus),
              'toStatus': _convertAlarmStatusToAlertStatusString(change.toStatus),
              'userId': change.userId,
              'userName': change.userName,
              'note': change.note,
            };
          }).toList(),
        });
      } catch (e) {
        // Handle error
      }
    }
  }

  Future<void> addReport(AlarmReport report, {String? userId, String? userName}) async {
    // Add report to local list
    _reports.add(report);

    // Save report to Firestore
    try {
      // Convert AlarmReport to a format suitable for Firestore
      final reportData = {
        'id': report.id,
        'alarmId': report.alarmId,
        'timestamp': Timestamp.fromDate(report.timestamp),
        'content': report.content,
        'authorName': report.authorName,
        'userId': userId,
      };

      // Add report to the alerts collection as a subcollection
      await _databaseService.alertsCollection
          .doc(report.alarmId)
          .collection('reports')
          .doc(report.id)
          .set(reportData);

      // Also update the alert document to include the report
      await _databaseService.updateAlert(report.alarmId, {
        'reports': FieldValue.arrayUnion([reportData]),
        'lastReportAt': Timestamp.fromDate(report.timestamp),
      });

      if (kDebugMode) {
        print('Report saved to Firestore successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving report to Firestore: $e');
      }
    }

    // Don't automatically mark the alarm as handled
    // Removed: await markAlarmAsHandled(report.alarmId, userId: userId, userName: userName);
  }

  // Hämta larm som är tilldelade till en användare
  List<Alarm> getAlarmsAssignedToUser(String userId) {
    return _alarms.where((alarm) => alarm.assignedUserId == userId).toList();
  }

  // Hämta larm som inte är tilldelade till någon
  List<Alarm> getUnassignedAlarms() {
    return _alarms.where((alarm) => alarm.assignedUserId == null).toList();
  }

  // Hämta aktiva larm (nya eller pågående)
  List<Alarm> getActiveAlarms() {
    return _alarms.where((alarm) =>
      alarm.status == AlarmStatus.new_ || alarm.status == AlarmStatus.ongoing
    ).toList();
  }

  // Hämta larm för en specifik butik
  List<Alarm> getAlarmsForStore(String storeId) {
    // Hämta alla enheter för butiken
    final storeService = StoreService();
    final devices = storeService.getDevicesForStore(storeId);
    final deviceIds = devices.map((device) => device.id).toList();

    // Filtrera larm baserat på enheternas ID
    return _alarms.where((alarm) => deviceIds.contains(alarm.deviceId)).toList();
  }

  List<AlarmReport> getReportsForAlarm(String alarmId) {
    // First get reports from local cache
    final localReports = _reports.where((report) => report.alarmId == alarmId).toList();

    // Fetch reports from Firestore in the background and update local cache
    _fetchReportsFromFirestore(alarmId);

    return localReports;
  }

  // Helper method to fetch reports from Firestore
  Future<void> _fetchReportsFromFirestore(String alarmId) async {
    try {
      // Get reports from the subcollection
      final reportsSnapshot = await _databaseService.alertsCollection
          .doc(alarmId)
          .collection('reports')
          .get();

      // Process reports
      for (var doc in reportsSnapshot.docs) {
        final data = doc.data();

        // Convert Firestore timestamp to DateTime
        final timestamp = data['timestamp'] is Timestamp
            ? (data['timestamp'] as Timestamp).toDate()
            : DateTime.fromMillisecondsSinceEpoch(data['timestamp']);

        final report = AlarmReport(
          id: data['id'],
          alarmId: alarmId,
          timestamp: timestamp,
          content: data['content'],
          authorName: data['authorName'],
        );

        // Check if this report already exists in our local list
        final existingIndex = _reports.indexWhere((r) => r.id == report.id);
        if (existingIndex >= 0) {
          // Update existing report
          _reports[existingIndex] = report;
        } else {
          // Add new report
          _reports.add(report);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching reports from Firestore: $e');
      }
    }
  }

  Alarm? getAlarmById(String id) {
    try {
      return _alarms.firstWhere((alarm) => alarm.id == id);
    } catch (e) {
      return null;
    }
  }

  // Convert Alert to Alarm
  Alarm _convertAlertToAlarm(Alert alert) {
    // Extract location from topic or use a default
    String location = 'Unknown';

    // Try to extract location from topic if available
    if (alert.topic != null && alert.topic!.isNotEmpty) {
      try {
        // Check if we can extract store name from topic
        // Format is typically /store/StoreName/button/type
        final topicParts = alert.topic!.split('/');
        if (topicParts.length >= 3 && topicParts[1] == 'store') {
          location = topicParts[2]; // Store name
        }
      } catch (e) {
        // Silently handle error, keep default location
      }
    }

    // Determine alarm type based on alertType
    AlarmType alarmType = AlarmType.red; // Default to red
    if (alert.alertType == AlertType.yellow) {
      alarmType = AlarmType.yellow;
    }

    // Determine alarm status based on alert status
    AlarmStatus alarmStatus = AlarmStatus.new_; // Default to new
    if (alert.status == AlertStatus.acknowledged) {
      alarmStatus = AlarmStatus.ongoing;
    } else if (alert.status == AlertStatus.resolved || alert.status == AlertStatus.false_alarm) {
      alarmStatus = AlarmStatus.resolved;
    }

    // Create status history if available
    List<AlarmStatusChange> statusHistory = [];
    for (var change in alert.statusHistory) {
      statusHistory.add(AlarmStatusChange(
        timestamp: change.timestamp,
        fromStatus: _convertAlertStatusToAlarmStatus(change.fromStatus),
        toStatus: _convertAlertStatusToAlarmStatus(change.toStatus),
        userId: change.userId,
        userName: change.userName,
        note: change.note,
      ));
    }

    return Alarm(
      id: alert.id,
      timestamp: alert.pressedAt,
      deviceId: alert.buttonId,
      location: location,
      type: alarmType,
      status: alarmStatus,
      assignedUserId: alert.handledBy,
      assignedUserName: alert.handlerName,
      statusHistory: statusHistory,
    );
  }

  // Convert AlertStatus to AlarmStatus
  AlarmStatus _convertAlertStatusToAlarmStatus(AlertStatus status) {
    switch (status) {
      case AlertStatus.active:
        return AlarmStatus.new_;
      case AlertStatus.acknowledged:
        return AlarmStatus.ongoing;
      case AlertStatus.resolved:
      case AlertStatus.false_alarm:
        return AlarmStatus.resolved;
    }
  }

  // Setup listener for alerts from Firestore
  void _setupAlertsListener() {
    _databaseService.getAlertsStream().listen((snapshot) {
      if (kDebugMode) {
        print('AlarmService received update with ${snapshot.docs.length} documents');
      }

      // Get current document IDs from the snapshot
      final currentIds = snapshot.docs.map((doc) => doc.id).toSet();

      // Find alarms to remove (those in _alarms but not in the current snapshot)
      final alarmsToRemove = _alarms.where((alarm) => !currentIds.contains(alarm.id)).toList();

      // Remove alarms that no longer exist in Firestore
      for (final alarm in alarmsToRemove) {
        if (kDebugMode) {
          print('Removing alarm with ID: ${alarm.id} from local cache');
        }
        _alarms.removeWhere((a) => a.id == alarm.id);

        // Notify listeners about the removed alarm
        if (_isActive && !_alarmStreamController.isClosed) {
          // We need to create a copy of the alarm with a special flag to indicate deletion
          final deletedAlarm = alarm.copyWith();
          _alarmStreamController.add(deletedAlarm);
        }
      }

      // Process current documents (add new ones, update existing ones)
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final alert = Alert.fromMap({
          'id': doc.id,
          ...data,
        });

        // Convert Alert to Alarm
        final alarm = _convertAlertToAlarm(alert);

        // Check if this alarm already exists in our list
        final existingIndex = _alarms.indexWhere((a) => a.id == alarm.id);
        if (existingIndex >= 0) {
          // Update existing alarm
          _alarms[existingIndex] = alarm;
        } else {
          // Add new alarm
          _alarms.add(alarm);

          // Notify listeners of new alarm
          if (_isActive && !_alarmStreamController.isClosed) {
            _alarmStreamController.add(alarm);
          }
        }
      }
    });
  }

  void dispose() {
    // Stäng streamcontroller om den är öppen
    if (!_alarmStreamController.isClosed) {
      _alarmStreamController.close();
    }
  }

  // Rensa alla prenumerationer (anropas vid utloggning)
  void clearSubscriptions() {
    // Markera tjänsten som inaktiv
    _isActive = false;

    // Stäng den befintliga streamcontrollern om den är öppen
    if (!_alarmStreamController.isClosed) {
      _alarmStreamController.close();
    }

    // Skapa en ny streamcontroller för framtida prenumerationer
    _alarmStreamController = StreamController<Alarm>.broadcast();

    // Clear existing alarms
    _alarms.clear();

    // Setup alerts listener again
    _setupAlertsListener();

    // Markera tjänsten som aktiv igen
    _isActive = true;
  }

  // Generera ett unikt ID för nya larm eller rapporter
  String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
           Random().nextInt(1000).toString();
  }

  // Markera alla nya larm som pågående
  Future<void> markAllAlarmsAsOngoing({String? userId, String? userName}) async {
    final newAlarms = _alarms.where((alarm) => alarm.status == AlarmStatus.new_).toList();
    for (final alarm in newAlarms) {
      await markAlarmAsOngoing(alarm.id, userId: userId, userName: userName);
    }
  }

  // Markera alla pågående larm som åtgärdade
  Future<void> markAllOngoingAlarmsAsResolved({String? userId, String? userName}) async {
    final ongoingAlarms = _alarms.where((alarm) => alarm.status == AlarmStatus.ongoing).toList();
    for (final alarm in ongoingAlarms) {
      await markAlarmAsHandled(alarm.id, userId: userId, userName: userName);
    }
  }

  // Markera alla nya och pågående larm som åtgärdade
  Future<void> markAllActiveAlarmsAsResolved({String? userId, String? userName}) async {
    final activeAlarms = getActiveAlarms();
    for (final alarm in activeAlarms) {
      await markAlarmAsHandled(alarm.id, userId: userId, userName: userName);
    }
  }
}
