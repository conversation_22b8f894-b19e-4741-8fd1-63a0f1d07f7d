import 'package:cloud_firestore/cloud_firestore.dart';

class Activity {
  final String id;
  final String userId;
  final String action;
  final DateTime timestamp;
  final Map<String, dynamic> meta;

  Activity({
    required this.id,
    required this.userId,
    required this.action,
    required this.timestamp,
    this.meta = const {},
  });

  Activity copyWith({
    String? id,
    String? userId,
    String? action,
    DateTime? timestamp,
    Map<String, dynamic>? meta,
  }) {
    return Activity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      action: action ?? this.action,
      timestamp: timestamp ?? this.timestamp,
      meta: meta ?? this.meta,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'action': action,
      'timestamp': Timestamp.fromDate(timestamp),
      'meta': meta,
    };
  }

  factory Activity.fromMap(Map<String, dynamic> map) {
    return Activity(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      action: map['action'] ?? '',
      timestamp: map['timestamp'] is Timestamp 
          ? (map['timestamp'] as Timestamp).toDate() 
          : DateTime.now(),
      meta: Map<String, dynamic>.from(map['meta'] ?? {}),
    );
  }
}
