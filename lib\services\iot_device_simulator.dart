import 'dart:async';
import 'dart:math';
import '../models/alarm.dart';
import 'alarm_service.dart';
import 'audio_service.dart';

class IoTDeviceSimulator {
  static final IoTDeviceSimulator _instance = IoTDeviceSimulator._internal();

  factory IoTDeviceSimulator() {
    return _instance;
  }

  IoTDeviceSimulator._internal();

  final AlarmService _alarmService = AlarmService();
  Timer? _simulationTimer;
  bool _isSimulating = false;

  // No hardcoded device IDs or locations - will be fetched from Firestore
  final List<String> _deviceIds = [];
  final List<String> _locations = [];

  // No hardcoded alarm probabilities
  final Map<String, Map<AlarmType, double>> _locationAlarmProbabilities = {};

  void startSimulation() {
    // Avbryt eventuell befintlig timer först
    stopSimulation();

    _isSimulating = true;
    // Ingen automatisk simulering längre
    // Användaren måste manuellt skapa larm
  }

  void stopSimulation() {
    // Avbryt timern om den körs
    if (_simulationTimer != null && _simulationTimer!.isActive) {
      _simulationTimer!.cancel();
      _simulationTimer = null;
    }
    _isSimulating = false;

    // IoT-simulatorn har stoppats
  }

  void _triggerRandomAlarm() {
    try {
      // Kontrollera om AlarmService är aktiv innan vi fortsätter
      if (!_alarmService.isActive) {
        // AlarmService är inte aktiv, avbryter larmgenerering
        return;
      }

      final deviceId = _deviceIds[Random().nextInt(_deviceIds.length)];
      final location = _locations[Random().nextInt(_locations.length)];

      // Bestäm larmtyp baserat på platsens sannolikheter
      final alarmTypeProbabilities = _locationAlarmProbabilities[location]!;
      final random = Random().nextDouble();
      final alarmType = random < alarmTypeProbabilities[AlarmType.yellow]!
          ? AlarmType.yellow
          : AlarmType.red;

      final alarm = Alarm(
        id: _alarmService.generateId(),
        timestamp: DateTime.now(),
        deviceId: deviceId,
        location: location,
        type: alarmType,
        status: AlarmStatus.new_,
      );

      // Försök skicka larmet, men fånga eventuella fel
      try {
        _alarmService.receiveAlarm(alarm);
      } catch (e) {
        // Fel vid mottagning av larm
        // Stoppa simuleringen om något går fel
        stopSimulation();
      }
    } catch (e) {
      // Fel vid generering av larm
      // Stoppa simuleringen om något går fel
      stopSimulation();
    }
  }

  // Manuellt trigga ett larm (för testning)
  void triggerManualAlarm({AlarmType? type}) {
    try {
      // Kontrollera om AlarmService är aktiv innan vi fortsätter
      if (!_alarmService.isActive) {
        // AlarmService är inte aktiv, avbryter larmgenerering
        return;
      }

      if (type != null) {
        final deviceId = _deviceIds[Random().nextInt(_deviceIds.length)];
        final location = _locations[Random().nextInt(_locations.length)];

        final alarm = Alarm(
          id: _alarmService.generateId(),
          timestamp: DateTime.now(),
          deviceId: deviceId,
          location: location,
          type: type,
          status: AlarmStatus.new_,
        );

        try {
          _alarmService.receiveAlarm(alarm);
        } catch (e) {
          // Fel vid mottagning av larm
          stopSimulation();
        }
      } else {
        _triggerRandomAlarm();
      }
    } catch (e) {
      // Fel vid generering av larm
      stopSimulation();
    }
  }

  bool get isSimulating => _isSimulating;
}
