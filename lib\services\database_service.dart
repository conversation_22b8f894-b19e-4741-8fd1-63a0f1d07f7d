import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user.dart';
import '../models/store.dart';
import '../models/button.dart';
import '../models/alert.dart';
import '../models/request.dart';
import '../models/shift.dart';
import '../models/report.dart';
import '../models/activity.dart';
import '../models/notification.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  factory DatabaseService() {
    return _instance;
  }

  DatabaseService._internal();

  // Collection references
  CollectionReference get usersCollection => _firestore.collection('users');
  CollectionReference get storesCollection => _firestore.collection('stores');
  CollectionReference get buttonsCollection => _firestore.collection('buttons');
  CollectionReference get alertsCollection => _firestore.collection('alerts');
  CollectionReference get requestsCollection => _firestore.collection('requests');
  CollectionReference get shiftsCollection => _firestore.collection('shifts');
  CollectionReference get reportsCollection => _firestore.collection('reports');
  CollectionReference get activitiesCollection => _firestore.collection('activities');
  CollectionReference get notificationsCollection => _firestore.collection('notifications');
  CollectionReference get mqttMessagesCollection => _firestore.collection('mqtt_messages');

  // Generic methods
  Future<DocumentSnapshot> getDocument(CollectionReference collection, String documentId) async {
    return await collection.doc(documentId).get();
  }

  Future<QuerySnapshot> getCollection(CollectionReference collection) async {
    return await collection.get();
  }

  Stream<DocumentSnapshot> documentStream(CollectionReference collection, String documentId) {
    return collection.doc(documentId).snapshots();
  }

  Stream<QuerySnapshot> collectionStream(CollectionReference collection) {
    return collection.snapshots();
  }

  // User methods
  Future<void> setUser(String userId, Map<String, dynamic> userData) async {
    await usersCollection.doc(userId).set(userData);
  }

  Future<DocumentSnapshot> getUser(String userId) async {
    return await usersCollection.doc(userId).get();
  }

  Future<QuerySnapshot> getUsersByRole(int roleIndex) async {
    return await usersCollection.where('role', isEqualTo: roleIndex).get();
  }

  Stream<QuerySnapshot> getUsersStream() {
    return usersCollection.snapshots();
  }

  // Store methods
  Future<void> setStore(String storeId, Map<String, dynamic> storeData) async {
    await storesCollection.doc(storeId).set(storeData);
  }

  Future<DocumentSnapshot> getStore(String storeId) async {
    return await storesCollection.doc(storeId).get();
  }

  Stream<QuerySnapshot> getStoresStream() {
    return storesCollection.snapshots();
  }

  // Button methods
  Future<void> setButton(String buttonId, Map<String, dynamic> buttonData) async {
    await buttonsCollection.doc(buttonId).set(buttonData);
  }

  Future<DocumentSnapshot> getButton(String buttonId) async {
    return await buttonsCollection.doc(buttonId).get();
  }

  Future<QuerySnapshot> getButtonsByStore(String storeId) async {
    return await buttonsCollection.where('storeId', isEqualTo: storeId).get();
  }

  Stream<QuerySnapshot> getButtonsStream() {
    return buttonsCollection.snapshots();
  }

  Stream<QuerySnapshot> getButtonsByStoreStream(String storeId) {
    return buttonsCollection.where('storeId', isEqualTo: storeId).snapshots();
  }

  // Alert methods
  Future<DocumentReference> addAlert(Map<String, dynamic> alertData) async {
    return await alertsCollection.add(alertData);
  }

  Future<void> updateAlert(String alertId, Map<String, dynamic> alertData) async {
    await alertsCollection.doc(alertId).update(alertData);
  }

  Future<DocumentSnapshot> getAlert(String alertId) async {
    return await alertsCollection.doc(alertId).get();
  }

  Future<QuerySnapshot> getAlertsByStore(String storeId) async {
    return await alertsCollection.where('storeId', isEqualTo: storeId).get();
  }

  Stream<QuerySnapshot> getAlertsStream() {
    // This stream will emit events for all changes to the collection
    // including document additions, modifications, and deletions
    return alertsCollection
        .orderBy('pressedAt', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> getAlertsByStoreStream(String storeId) {
    return alertsCollection
        .where('storeId', isEqualTo: storeId)
        .orderBy('pressedAt', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> getActiveAlertsStream() {
    // This stream will emit events for all changes to active alerts
    // including document additions, modifications, and deletions
    return alertsCollection
        .where('status', isEqualTo: 'active')
        .orderBy('pressedAt', descending: true)
        .snapshots();
  }

  // Get a stream of new alerts created in the last minute
  Stream<QuerySnapshot> getRecentAlertsStream() {
    // Calculate timestamp for 2 minutes ago (to be safe)
    final twoMinutesAgo = Timestamp.fromDate(
      DateTime.now().subtract(const Duration(minutes: 2))
    );

    // Listen for alerts created in the last 2 minutes
    // This ensures we don't miss any alerts, and we'll filter them further in the listener
    return alertsCollection
        .where('pressedAt', isGreaterThan: twoMinutesAgo)
        .orderBy('pressedAt', descending: true)
        .limit(10) // Limit to 10 most recent alerts to avoid processing too many
        .snapshots();
  }

  // Request methods
  Future<DocumentReference> addRequest(Map<String, dynamic> requestData) async {
    return await requestsCollection.add(requestData);
  }

  Future<void> updateRequest(String requestId, Map<String, dynamic> requestData) async {
    await requestsCollection.doc(requestId).update(requestData);
  }

  Future<DocumentSnapshot> getRequest(String requestId) async {
    return await requestsCollection.doc(requestId).get();
  }

  Future<QuerySnapshot> getRequestsByStore(String storeId) async {
    return await requestsCollection.where('storeId', isEqualTo: storeId).get();
  }

  Stream<QuerySnapshot> getRequestsStream() {
    return requestsCollection.snapshots();
  }

  Stream<QuerySnapshot> getRequestsByStoreStream(String storeId) {
    return requestsCollection.where('storeId', isEqualTo: storeId).snapshots();
  }

  // Shift methods
  Future<DocumentReference> addShift(Map<String, dynamic> shiftData) async {
    return await shiftsCollection.add(shiftData);
  }

  Future<void> updateShift(String shiftId, Map<String, dynamic> shiftData) async {
    await shiftsCollection.doc(shiftId).update(shiftData);
  }

  Future<DocumentSnapshot> getShift(String shiftId) async {
    return await shiftsCollection.doc(shiftId).get();
  }

  Future<QuerySnapshot> getShiftsByGuard(String guardId) async {
    return await shiftsCollection.where('guardId', isEqualTo: guardId).get();
  }

  Stream<QuerySnapshot> getShiftsStream() {
    return shiftsCollection.snapshots();
  }

  Stream<QuerySnapshot> getActiveShiftsStream() {
    return shiftsCollection.where('status', isEqualTo: 'active').snapshots();
  }

  // Report methods
  Future<DocumentReference> addReport(Map<String, dynamic> reportData) async {
    return await reportsCollection.add(reportData);
  }

  Future<DocumentSnapshot> getReport(String reportId) async {
    return await reportsCollection.doc(reportId).get();
  }

  Future<QuerySnapshot> getReportsByStore(String storeId) async {
    return await reportsCollection.where('storeId', isEqualTo: storeId).get();
  }

  Stream<QuerySnapshot> getReportsStream() {
    return reportsCollection.snapshots();
  }

  // Activity methods
  Future<DocumentReference> addActivity(Map<String, dynamic> activityData) async {
    return await activitiesCollection.add(activityData);
  }

  Stream<QuerySnapshot> getActivitiesStream() {
    return activitiesCollection.orderBy('timestamp', descending: true).snapshots();
  }

  // Notification methods
  Future<DocumentReference> addNotification(Map<String, dynamic> notificationData) async {
    return await notificationsCollection.add(notificationData);
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    await notificationsCollection.doc(notificationId).update({'read': true});
  }

  Stream<QuerySnapshot> getUserNotificationsStream(String userId) {
    return notificationsCollection
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  // MQTT messages methods
  Stream<QuerySnapshot> getMqttMessagesStream() {
    return mqttMessagesCollection
        .orderBy('receivedAt', descending: true)
        .snapshots();
  }
}
