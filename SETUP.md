# Larmknapp App Setup Guide

This guide will help you set up the <PERSON><PERSON><PERSON><PERSON><PERSON> security application with your own Firebase project.

## Prerequisites

- Flutter SDK installed
- Firebase CLI installed
- Node.js installed (for Firebase Functions)
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

## Firebase Setup

### 1. Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Enable Authentication, Firestore, and Cloud Functions

### 2. Configure Firebase for Flutter

1. Install FlutterFire CLI:
   ```bash
   dart pub global activate flutterfire_cli
   ```

2. Configure Firebase for your project:
   ```bash
   flutterfire configure
   ```
   
   This will:
   - Create `lib/firebase_options.dart`
   - Create `android/app/google-services.json`
   - Create `ios/Runner/GoogleService-Info.plist`

### 3. Set up Firebase Authentication

1. In Firebase Console, go to Authentication > Sign-in method
2. Enable Email/Password authentication

### 4. Set up Firestore Database

1. In Firebase Console, go to Firestore Database
2. Create database in production mode
3. Update security rules using the provided `firestore.rules` file

### 5. Set up Cloud Functions

1. Navigate to the functions directory:
   ```bash
   cd functions
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Deploy functions:
   ```bash
   firebase deploy --only functions
   ```

## Configuration Files Setup

### 1. Service Account Key

1. In Firebase Console, go to Project Settings > Service Accounts
2. Generate a new private key
3. Save it as `serviceAccountKey.json` in the project root
4. Use the template `serviceAccountKey.json.template` as reference

### 2. Local Properties (Android)

1. Copy `android/local.properties.template` to `android/local.properties`
2. Update the paths to match your local Android SDK and Flutter installation

### 3. User Creation Scripts

1. Copy the template files and remove `.template` extension:
   - `create-admin-user.js.template` → `create-admin-user.js`
   - `set-admin-claim.js.template` → `set-admin-claim.js`

2. Update the credentials in these files with your desired admin credentials

## Creating Initial Users

### Option 1: Using the Admin Creation Script

1. Update `create-admin-user.js` with your desired credentials
2. Run the script:
   ```bash
   node create-admin-user.js
   ```

### Option 2: Manual User Creation

1. Create a user in Firebase Authentication
2. Update `set-admin-claim.js` with the user ID
3. Run the script:
   ```bash
   node set-admin-claim.js
   ```
4. Manually create the user document in Firestore as instructed

## Security Considerations

### Files to Keep Secret

The following files contain sensitive information and should NEVER be committed to version control:

- `serviceAccountKey.json` - Firebase service account private key
- `lib/firebase_options.dart` - Firebase API keys and configuration
- `android/app/google-services.json` - Android Firebase configuration
- `ios/Runner/GoogleService-Info.plist` - iOS Firebase configuration
- `android/local.properties` - Local development paths
- `create-admin-user.js` - Contains hardcoded credentials
- `set-admin-claim.js` - Contains user IDs

These files are already added to `.gitignore` to prevent accidental commits.

## Running the App

1. Ensure all configuration files are in place
2. Run Flutter pub get:
   ```bash
   flutter pub get
   ```

3. Run the app:
   ```bash
   flutter run
   ```

## Deployment

### Android APK

```bash
flutter build apk --release
```

### iOS App

```bash
flutter build ios --release
```

## Troubleshooting

### Firebase Permission Errors

If you encounter permission denied errors:
1. Check that Firestore security rules are properly configured
2. Ensure user has the correct custom claims set
3. Verify that the user document exists in Firestore

### Authentication Issues

1. Ensure Firebase Authentication is enabled
2. Check that the user exists in Firebase Auth
3. Verify custom claims are set correctly

## Support

For issues related to Firebase setup, refer to the [Firebase documentation](https://firebase.google.com/docs).
For Flutter-specific issues, refer to the [Flutter documentation](https://flutter.dev/docs).
