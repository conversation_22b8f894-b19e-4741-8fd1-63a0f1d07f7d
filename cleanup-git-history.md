# Git History Cleanup Guide

This guide will help you remove sensitive files from your Git history on the "Database" branch.

## ⚠️ IMPORTANT WARNING ⚠️

**This process will rewrite Git history and cannot be undone. Make sure you have backups of your repository before proceeding.**

## Option 1: Using BFG Repo-Cleaner (Recommended)

BFG Repo-Cleaner is faster and easier to use than git filter-branch.

### Step 1: Install BFG Repo-Cleaner

Download from: https://rtyley.github.io/bfg-repo-cleaner/

### Step 2: Create a fresh clone

```bash
git clone --mirror https://github.com/yourusername/your-repo.git
cd your-repo.git
```

### Step 3: Remove sensitive files

```bash
# Remove specific files
java -jar bfg.jar --delete-files serviceAccountKey.json
java -jar bfg.jar --delete-files firebase_options.dart
java -jar bfg.jar --delete-files google-services.json
java -jar bfg.jar --delete-files GoogleService-Info.plist
java -jar bfg.jar --delete-files local.properties
java -jar bfg.jar --delete-files create-admin-user.js
java -jar bfg.jar --delete-files set-admin-claim.js
java -jar bfg.jar --delete-files create-auth-user-only.js

# Clean up the repository
git reflog expire --expire=now --all && git gc --prune=now --aggressive
```

### Step 4: Push the cleaned history

```bash
git push --force
```

## Option 2: Using git filter-branch

### Step 1: Backup your repository

```bash
git clone your-repo.git your-repo-backup
```

### Step 2: Switch to the Database branch

```bash
git checkout Database
```

### Step 3: Remove files from history

```bash
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch serviceAccountKey.json lib/firebase_options.dart android/app/google-services.json ios/Runner/GoogleService-Info.plist android/local.properties create-admin-user.js set-admin-claim.js create-auth-user-only.js' \
  --prune-empty --tag-name-filter cat -- --all
```

### Step 4: Clean up

```bash
git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin
git reflog expire --expire=now --all
git gc --prune=now
```

### Step 5: Force push

```bash
git push --force origin Database
```

## Option 3: Using git filter-repo (Modern Alternative)

If you have git-filter-repo installed:

```bash
# Install git-filter-repo first
pip install git-filter-repo

# Remove files from history
git filter-repo --path serviceAccountKey.json --invert-paths
git filter-repo --path lib/firebase_options.dart --invert-paths
git filter-repo --path android/app/google-services.json --invert-paths
git filter-repo --path ios/Runner/GoogleService-Info.plist --invert-paths
git filter-repo --path android/local.properties --invert-paths
git filter-repo --path create-admin-user.js --invert-paths
git filter-repo --path set-admin-claim.js --invert-paths
git filter-repo --path create-auth-user-only.js --invert-paths

# Force push
git push --force origin Database
```

## After Cleanup

### 1. Verify the cleanup

Check that sensitive files are no longer in the history:

```bash
git log --all --full-history -- serviceAccountKey.json
git log --all --full-history -- lib/firebase_options.dart
```

These commands should return no results.

### 2. Update .gitignore

Make sure your `.gitignore` file includes all sensitive files (already done in this project).

### 3. Notify collaborators

If others have cloned the repository, they need to:

```bash
git fetch origin
git reset --hard origin/Database
```

Or clone the repository fresh.

### 4. Regenerate secrets

Since the secrets were exposed in Git history:

1. **Regenerate Firebase service account key**
2. **Rotate Firebase API keys** (create new Firebase apps if necessary)
3. **Change any hardcoded passwords**

## Verification Commands

After cleanup, verify that secrets are removed:

```bash
# Search for API keys in history
git log --all -S "AIzaSy" --source --all

# Search for private keys in history
git log --all -S "BEGIN PRIVATE KEY" --source --all

# Search for specific project ID
git log --all -S "larmknapp-52953" --source --all
```

## Additional Security Measures

1. **Enable GitHub secret scanning** (if using GitHub)
2. **Use environment variables** for sensitive configuration
3. **Implement pre-commit hooks** to prevent future secret commits
4. **Regular security audits** of your codebase

## Recovery

If something goes wrong, you can restore from your backup:

```bash
cd your-repo-backup
git push --force origin Database
```
