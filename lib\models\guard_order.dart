import 'package:flutter/material.dart';

enum GuardType {
  entranceHost, // Entrévärd
  securityGuard, // Ordningsvakt
  guard, // Väktare
}

enum OrderStatus {
  pending, // Väntar på godkännande
  approved, // <PERSON>k<PERSON>nd
  rejected, // Avvisad
  completed, // <PERSON>omförd
  cancelled, // Avbruten
}

// Klass för att spåra statusändringar
class OrderStatusChange {
  final DateTime timestamp;
  final OrderStatus fromStatus;
  final OrderStatus toStatus;
  final String? userId;
  final String? userName;
  final String? note;

  OrderStatusChange({
    required this.timestamp,
    required this.fromStatus,
    required this.toStatus,
    this.userId,
    this.userName,
    this.note,
  });
}

class GuardOrder {
  final String id;
  final String storeId;
  final String storeName;
  final DateTime createdAt;
  final DateTime requestedDate;
  final String startTime;
  final String endTime;
  final GuardType guardType;
  final int numberOfGuards;
  final String description;
  final OrderStatus status;
  final String? adminFeedback;
  final DateTime? updatedAt;
  final List<String> attachmentPaths; // Sökvägar till bifogade filer
  final String? securityGuardPermitPath; // Sökväg till tillstånd för ordningsvakt
  final List<OrderStatusChange> statusHistory; // Historik över statusändringar
  final String? assignedUserId; // ID för tilldelad användare (t.ex. vakt)
  final String? assignedUserName; // Namn på tilldelad användare

  GuardOrder({
    required this.id,
    required this.storeId,
    required this.storeName,
    required this.createdAt,
    required this.requestedDate,
    required this.startTime,
    required this.endTime,
    required this.guardType,
    required this.numberOfGuards,
    required this.description,
    this.status = OrderStatus.pending,
    this.adminFeedback,
    this.updatedAt,
    this.attachmentPaths = const [],
    this.securityGuardPermitPath,
    this.statusHistory = const [],
    this.assignedUserId,
    this.assignedUserName,
  });

  GuardOrder copyWith({
    String? id,
    String? storeId,
    String? storeName,
    DateTime? createdAt,
    DateTime? requestedDate,
    String? startTime,
    String? endTime,
    GuardType? guardType,
    int? numberOfGuards,
    String? description,
    OrderStatus? status,
    String? adminFeedback,
    DateTime? updatedAt,
    List<String>? attachmentPaths,
    String? securityGuardPermitPath,
    List<OrderStatusChange>? statusHistory,
    String? assignedUserId,
    String? assignedUserName,
  }) {
    return GuardOrder(
      id: id ?? this.id,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      createdAt: createdAt ?? this.createdAt,
      requestedDate: requestedDate ?? this.requestedDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      guardType: guardType ?? this.guardType,
      numberOfGuards: numberOfGuards ?? this.numberOfGuards,
      description: description ?? this.description,
      status: status ?? this.status,
      adminFeedback: adminFeedback ?? this.adminFeedback,
      updatedAt: updatedAt ?? this.updatedAt,
      attachmentPaths: attachmentPaths ?? this.attachmentPaths,
      securityGuardPermitPath: securityGuardPermitPath ?? this.securityGuardPermitPath,
      statusHistory: statusHistory ?? this.statusHistory,
      assignedUserId: assignedUserId ?? this.assignedUserId,
      assignedUserName: assignedUserName ?? this.assignedUserName,
    );
  }

  // Hjälpmetoder för att få läsbara värden
  String get guardTypeText {
    switch (guardType) {
      case GuardType.entranceHost:
        return 'Entrévärd';
      case GuardType.securityGuard:
        return 'Ordningsvakt';
      case GuardType.guard:
        return 'Väktare';
    }
  }

  String get statusText {
    switch (status) {
      case OrderStatus.pending:
        return 'Väntar på godkännande';
      case OrderStatus.approved:
        return 'Godkänd';
      case OrderStatus.rejected:
        return 'Avvisad';
      case OrderStatus.completed:
        return 'Genomförd';
      case OrderStatus.cancelled:
        return 'Avbruten';
    }
  }

  Color get statusColor {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.approved:
        return Colors.green;
      case OrderStatus.rejected:
        return Colors.red;
      case OrderStatus.completed:
        return Colors.blue;
      case OrderStatus.cancelled:
        return Colors.grey;
    }
  }
}
