import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../services/user_service.dart';

class UserDetailScreen extends StatefulWidget {
  final bool isNewUser;
  final User? user;
  
  const UserDetailScreen({
    super.key,
    required this.isNewUser,
    this.user,
  });
  
  @override
  State<UserDetailScreen> createState() => _UserDetailScreenState();
}

class _UserDetailScreenState extends State<UserDetailScreen> {
  final _formKey = GlobalKey<FormState>();
  final UserService _userService = UserService();
  
  late TextEditingController _nameController;
  late TextEditingController _usernameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _positionController;
  late TextEditingController _departmentController;
  late TextEditingController _passwordController;
  late TextEditingController _confirmPasswordController;
  
  UserRole _selectedRole = UserRole.guard;
  UserStatus _selectedStatus = UserStatus.active;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  
  @override
  void initState() {
    super.initState();
    
    // Initiera kontroller med befintliga värden eller tomma strängar
    final user = widget.user;
    _nameController = TextEditingController(text: user?.name ?? '');
    _usernameController = TextEditingController(text: user?.username ?? '');
    _emailController = TextEditingController(text: user?.email ?? '');
    _phoneController = TextEditingController(text: user?.phoneNumber ?? '');
    _positionController = TextEditingController(text: user?.position ?? '');
    _departmentController = TextEditingController(text: user?.department ?? '');
    _passwordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
    
    if (user != null) {
      _selectedRole = user.role;
      _selectedStatus = user.status;
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _positionController.dispose();
    _departmentController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }
  
  void _saveUser() {
    if (!_formKey.currentState!.validate()) return;
    
    // För nya användare eller lösenordsändring
    if (widget.isNewUser || _passwordController.text.isNotEmpty) {
      if (_passwordController.text != _confirmPasswordController.text) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Lösenorden matchar inte'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }
    
    if (widget.isNewUser) {
      // Skapa ny användare
      final newUser = User(
        id: _userService.generateId(),
        username: _usernameController.text,
        name: _nameController.text,
        role: _selectedRole,
        email: _emailController.text,
        phoneNumber: _phoneController.text,
        position: _positionController.text,
        department: _departmentController.text,
        status: _selectedStatus,
      );
      
      final success = _userService.addUser(newUser, _passwordController.text);
      
      if (success) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Användare skapad'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Användarnamnet finns redan'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      // Uppdatera befintlig användare
      final updatedUser = widget.user!.copyWith(
        name: _nameController.text,
        email: _emailController.text,
        phoneNumber: _phoneController.text,
        position: _positionController.text,
        department: _departmentController.text,
        role: _selectedRole,
        status: _selectedStatus,
      );
      
      final success = _userService.updateUser(updatedUser);
      
      if (success) {
        // Uppdatera lösenord om det har angetts
        if (_passwordController.text.isNotEmpty) {
          _userService.updatePassword(updatedUser.id, _passwordController.text);
        }
        
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Användare uppdaterad'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Ett fel uppstod'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isNewUser ? 'Ny användare' : 'Redigera användare'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveUser,
            tooltip: 'Spara',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Användarinformation
              const Text(
                'Användarinformation',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Namn
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Namn *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Ange namn';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // Användarnamn (endast redigerbart för nya användare)
              TextFormField(
                controller: _usernameController,
                decoration: const InputDecoration(
                  labelText: 'Användarnamn *',
                  border: OutlineInputBorder(),
                ),
                enabled: widget.isNewUser, // Kan inte ändra användarnamn för befintliga användare
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Ange användarnamn';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // E-post
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'E-post *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Ange e-post';
                  }
                  if (!value.contains('@')) {
                    return 'Ange en giltig e-postadress';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // Telefon
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Telefon',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 24),
              
              // Arbetsuppgifter
              const Text(
                'Arbetsuppgifter',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Position
              TextFormField(
                controller: _positionController,
                decoration: const InputDecoration(
                  labelText: 'Position',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              
              // Avdelning
              TextFormField(
                controller: _departmentController,
                decoration: const InputDecoration(
                  labelText: 'Avdelning',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              
              // Roll
              DropdownButtonFormField<UserRole>(
                value: _selectedRole,
                decoration: const InputDecoration(
                  labelText: 'Roll *',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: UserRole.guard,
                    child: Text('Vakt'),
                  ),
                  DropdownMenuItem(
                    value: UserRole.admin,
                    child: Text('Administratör'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedRole = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              
              // Status
              DropdownButtonFormField<UserStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status *',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: UserStatus.active,
                    child: Text('Aktiv'),
                  ),
                  DropdownMenuItem(
                    value: UserStatus.inactive,
                    child: Text('Inaktiv'),
                  ),
                  DropdownMenuItem(
                    value: UserStatus.onLeave,
                    child: Text('Ledig'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 24),
              
              // Lösenord
              const Text(
                'Lösenord',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              if (!widget.isNewUser)
                const Text(
                  'Lämna tomt om du inte vill ändra lösenordet',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              const SizedBox(height: 16),
              
              // Lösenord
              TextFormField(
                controller: _passwordController,
                obscureText: _obscurePassword,
                decoration: InputDecoration(
                  labelText: 'Lösenord ${widget.isNewUser ? '*' : ''}',
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword ? Icons.visibility : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
                validator: (value) {
                  if (widget.isNewUser && (value == null || value.isEmpty)) {
                    return 'Ange lösenord';
                  }
                  if (value != null && value.isNotEmpty && value.length < 6) {
                    return 'Lösenordet måste vara minst 6 tecken';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // Bekräfta lösenord
              TextFormField(
                controller: _confirmPasswordController,
                obscureText: _obscureConfirmPassword,
                decoration: InputDecoration(
                  labelText: 'Bekräfta lösenord ${widget.isNewUser ? '*' : ''}',
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                  ),
                ),
                validator: (value) {
                  if (widget.isNewUser && (value == null || value.isEmpty)) {
                    return 'Bekräfta lösenordet';
                  }
                  if (_passwordController.text.isNotEmpty && value != _passwordController.text) {
                    return 'Lösenorden matchar inte';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 24),
              
              // Spara-knapp
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveUser,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    widget.isNewUser ? 'SKAPA ANVÄNDARE' : 'SPARA ÄNDRINGAR',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
