import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/alert_status_utils.dart';

enum AlertStatus { active, acknowledged, resolved, false_alarm }
enum AlertType { red, yellow, unknown }

class Alert {
  final String id;
  final String buttonId;
  final String storeId;
  final DateTime pressedAt;
  final AlertStatus status;
  final AlertType alertType;
  final DateTime? resolvedAt;
  final String? handledBy;
  final String? handlerName;
  final String? mqttMessageId;
  final String? payload;
  final String? topic;
  final List<AlertStatusChange> statusHistory;
  final List<Map<String, dynamic>> reports;

  Alert({
    required this.id,
    required this.buttonId,
    required this.storeId,
    required this.pressedAt,
    this.status = AlertStatus.active,
    this.alertType = AlertType.unknown,
    this.resolvedAt,
    this.handledBy,
    this.handlerName,
    this.mqttMessageId,
    this.payload,
    this.topic,
    this.statusHistory = const [],
    this.reports = const [],
  });

  bool get isActive => status == AlertStatus.active || status == AlertStatus.acknowledged;
  bool get isResolved => status == AlertStatus.resolved || status == AlertStatus.false_alarm;

  Alert copyWith({
    String? id,
    String? buttonId,
    String? storeId,
    DateTime? pressedAt,
    AlertStatus? status,
    AlertType? alertType,
    DateTime? resolvedAt,
    String? handledBy,
    String? handlerName,
    String? mqttMessageId,
    String? payload,
    String? topic,
    List<AlertStatusChange>? statusHistory,
    List<Map<String, dynamic>>? reports,
  }) {
    return Alert(
      id: id ?? this.id,
      buttonId: buttonId ?? this.buttonId,
      storeId: storeId ?? this.storeId,
      pressedAt: pressedAt ?? this.pressedAt,
      status: status ?? this.status,
      alertType: alertType ?? this.alertType,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      handledBy: handledBy ?? this.handledBy,
      handlerName: handlerName ?? this.handlerName,
      mqttMessageId: mqttMessageId ?? this.mqttMessageId,
      payload: payload ?? this.payload,
      topic: topic ?? this.topic,
      statusHistory: statusHistory ?? this.statusHistory,
      reports: reports ?? this.reports,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'buttonId': buttonId,
      'storeId': storeId,
      'pressedAt': Timestamp.fromDate(pressedAt),
      'status': AlertStatusUtils.statusToString(status),
      'alertType': _alertTypeToString(alertType),
      'resolvedAt': resolvedAt != null ? Timestamp.fromDate(resolvedAt!) : null,
      'handledBy': handledBy,
      'handlerName': handlerName,
      'mqttMessageId': mqttMessageId,
      'payload': payload,
      'topic': topic,
      'statusHistory': statusHistory.map((change) => change.toMap()).toList(),
      'reports': reports,
    };
  }

  factory Alert.fromMap(Map<String, dynamic> map) {
    return Alert(
      id: map['id'] ?? '',
      buttonId: map['buttonId'] ?? '',
      storeId: map['storeId'] ?? '',
      pressedAt: map['pressedAt'] is Timestamp
          ? (map['pressedAt'] as Timestamp).toDate()
          : DateTime.now(),
      status: AlertStatusUtils.stringToStatus(map['status']),
      alertType: _stringToAlertType(map['alertType']),
      resolvedAt: map['resolvedAt'] is Timestamp
          ? (map['resolvedAt'] as Timestamp).toDate()
          : null,
      handledBy: map['handledBy'],
      handlerName: map['handlerName'],
      mqttMessageId: map['mqttMessageId'],
      payload: map['payload'],
      topic: map['topic'],
      statusHistory: map['statusHistory'] != null
          ? List<AlertStatusChange>.from(
              (map['statusHistory'] as List).map(
                (x) => AlertStatusChange.fromMap(x),
              ),
            )
          : [],
      reports: map['reports'] != null
          ? List<Map<String, dynamic>>.from(
              (map['reports'] as List).map(
                (x) => Map<String, dynamic>.from(x),
              ),
            )
          : [],
    );
  }

  static String _alertTypeToString(AlertType alertType) {
    switch (alertType) {
      case AlertType.red:
        return 'red';
      case AlertType.yellow:
        return 'yellow';
      case AlertType.unknown:
        return 'unknown';
    }
  }

  static AlertType _stringToAlertType(String? alertType) {
    switch (alertType) {
      case 'red':
        return AlertType.red;
      case 'yellow':
        return AlertType.yellow;
      default:
        return AlertType.unknown;
    }
  }
}

class AlertStatusChange {
  final DateTime timestamp;
  final AlertStatus fromStatus;
  final AlertStatus toStatus;
  final String? userId;
  final String? userName;
  final String? note;

  AlertStatusChange({
    required this.timestamp,
    required this.fromStatus,
    required this.toStatus,
    this.userId,
    this.userName,
    this.note,
  });

  Map<String, dynamic> toMap() {
    return {
      'timestamp': Timestamp.fromDate(timestamp),
      'fromStatus': AlertStatusUtils.statusToString(fromStatus),
      'toStatus': AlertStatusUtils.statusToString(toStatus),
      'userId': userId,
      'userName': userName,
      'note': note,
    };
  }

  factory AlertStatusChange.fromMap(Map<String, dynamic> map) {
    return AlertStatusChange(
      timestamp: map['timestamp'] is Timestamp
          ? (map['timestamp'] as Timestamp).toDate()
          : DateTime.now(),
      fromStatus: AlertStatusUtils.stringToStatus(map['fromStatus']),
      toStatus: AlertStatusUtils.stringToStatus(map['toStatus']),
      userId: map['userId'],
      userName: map['userName'],
      note: map['note'],
    );
  }
}
