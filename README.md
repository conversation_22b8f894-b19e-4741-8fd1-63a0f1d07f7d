# larmknapp_app

A Flutter security application for alarm management.

# NOW:

- Mqtt handler function should parse the topic and add the store as store ID
- <PERSON><PERSON> should see a list of "vakt" in list


# TODO:
- IoT HTTP function only sends from 1 topic now, make sure it sends everything
- mqtt handler <PERSON><PERSON> should not change
- The endpoint function https://mqttwebhook-jtc7nletgq-ey.a.run.app should only accept from EQMX
- Secure communication between IoT device to cloud (man in the middle attack)
- Configure App Check to protect from resource abuse

## Getting Started

This project is a Flutter application with Firebase integration.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Firebase Integration Guide

This application uses Firebase for authentication, database, and storage. Below is a guide on how to work with Firebase in this project.

### Prerequisites

- Make sure you have a Firebase account
- Ensure you have the Firebase CLI installed (`npm install -g firebase-tools`)
- Flutter and Dart SDK installed

### Firebase Setup

The project is already configured with Firebase. The configuration files are:
- `android/app/google-services.json` for Android
- `ios/Runner/GoogleService-Info.plist` for iOS
- `lib/firebase_options.dart` for Flutter

If you need to connect to a different Firebase project:

1. Create a new project in the [Firebase Console](https://console.firebase.google.com/)
2. Install FlutterFire CLI: `dart pub global activate flutterfire_cli`
3. Run: `flutterfire configure` and follow the prompts
4. Select your new Firebase project and platforms (Android/iOS)

### Using Firebase in the App

#### Initialization

Firebase is initialized in `main.dart`:

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Rest of your app initialization
}
```

#### Authentication

The app uses Firebase Authentication with email/password. See `lib/services/auth_service.dart` for implementation details.

Example of signing in:

```dart
final FirebaseAuth _auth = FirebaseAuth.instance;
UserCredential result = await _auth.signInWithEmailAndPassword(
  email: email,
  password: password,
);
```

#### Firestore Database

The app uses Cloud Firestore for data storage. See `lib/services/firestore_service.dart` for implementation details.

Main collections:
- `users` - User profiles and roles
- `stores` - Store information
- `alarms` - Alarm events
- `devices` - IoT devices
- `guardOrders` - Security guard orders
- `reports` - Incident reports
- `activityLogs` - System activity logs

Example of reading data:

```dart
final FirebaseFirestore _firestore = FirebaseFirestore.instance;
QuerySnapshot snapshot = await _firestore.collection('users').get();
```

Example of writing data:

```dart
await _firestore.collection('users').doc(userId).set({
  'name': 'User Name',
  'email': '<EMAIL>',
  'role': 0,
});
```

#### Firebase Storage

The app uses Firebase Storage for file uploads. Example:

```dart
final FirebaseStorage _storage = FirebaseStorage.instance;
final Reference ref = _storage.ref().child('reports/$fileName');
await ref.putFile(file);
String downloadUrl = await ref.getDownloadURL();
```

### Security Rules

⚠️ **IMPORTANT**: The Firebase security rules are in production mode. You must update the rules each time you make changes to the database structure or access patterns.

To update security rules:
1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to Firestore Database > Rules
4. Update the rules and publish

Example of Firestore security rules:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Authentication check
    function isAuthenticated() {
      return request.auth != null;
    }

    // Admin check
    function isAdmin() {
      return isAuthenticated() &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 1;
    }

    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || request.auth.uid == userId;
    }

    // Other collections...
  }
}
```

### Firebase Indexes

⚠️ **IMPORTANT**: When adding complex queries with multiple field conditions or ordering, you need to create indexes in Firebase.

When you run a query that requires an index, Firestore will show an error with a direct link to create the required index. Click the link to create it.

Example of a query that requires an index:

```dart
// This query with multiple conditions and ordering requires an index
QuerySnapshot snapshot = await _firestore
    .collection('alarms')
    .where('storeId', isEqualTo: storeId)
    .where('status', isEqualTo: 0)
    .orderBy('timestamp', descending: true)
    .get();
```

To manually create indexes:
1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to Firestore Database > Indexes
4. Click "Add Index"
5. Select the collection, fields to index, and query scope

### Troubleshooting

1. **Authentication Issues**:
   - Check if the user exists in Firebase Authentication
   - Verify the user has a corresponding document in the `users` collection

2. **Permission Denied Errors**:
   - Check the security rules in Firebase Console
   - Ensure the user has the correct role assigned

3. **Missing Indexes**:
   - Follow the link in the error message to create the required index
   - Wait for the index to be built (can take a few minutes)

4. **Deployment Issues**:
   - Verify that the correct Firebase project is selected
   - Check that all configuration files are up to date
