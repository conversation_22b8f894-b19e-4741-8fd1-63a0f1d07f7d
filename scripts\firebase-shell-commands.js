// Function to get user claims
// Usage in Firebase shell: getUserClaims('USER_ID_HERE')
function getUserClaims(uid) {
  return admin.auth().getUser(uid)
    .then(userRecord => {
      console.log('User details:');
      console.log('  UID:', userRecord.uid);
      console.log('  Email:', userRecord.email);
      console.log('  Display name:', userRecord.displayName || 'Not set');
      console.log('\nCustom claims:');
      console.log(JSON.stringify(userRecord.customClaims, null, 2) || 'No custom claims set');
      return userRecord.customClaims;
    })
    .catch(error => {
      console.error('Error fetching user:', error);
      return null;
    });
}

// Function to list all users with their claims
// Usage in Firebase shell: listAllUsersWithClaims()
function listAllUsersWithClaims(nextPageToken) {
  const results = [];
  
  function listBatch(pageToken) {
    return admin.auth().listUsers(1000, pageToken)
      .then(listUsersResult => {
        listUsersResult.users.forEach(userRecord => {
          if (userRecord.customClaims) {
            results.push({
              uid: userRecord.uid,
              email: userRecord.email,
              claims: userRecord.customClaims
            });
          }
        });
        
        if (listUsersResult.pageToken) {
          // List next batch of users
          return listBatch(listUsersResult.pageToken);
        }
        
        console.log('Users with custom claims:');
        console.log(JSON.stringify(results, null, 2));
        return results;
      });
  }
  
  return listBatch(nextPageToken);
}
