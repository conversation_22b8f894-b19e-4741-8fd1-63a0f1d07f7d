import 'package:cloud_firestore/cloud_firestore.dart';

enum RequestStatus { pending, approved, denied }

class Request {
  final String id;
  final String storeId;
  final DateTime requestedAt;
  final DateTime startTime;
  final DateTime endTime;
  final String role;
  final int count;
  final String description;
  final String? attachmentUrl;
  final RequestStatus status;
  final AdminResponse? adminResponse;

  Request({
    required this.id,
    required this.storeId,
    required this.requestedAt,
    required this.startTime,
    required this.endTime,
    required this.role,
    required this.count,
    required this.description,
    this.attachmentUrl,
    this.status = RequestStatus.pending,
    this.adminResponse,
  });

  bool get isPending => status == RequestStatus.pending;
  bool get isApproved => status == RequestStatus.approved;
  bool get isDenied => status == RequestStatus.denied;

  Request copyWith({
    String? id,
    String? storeId,
    DateTime? requestedAt,
    DateTime? startTime,
    DateTime? endTime,
    String? role,
    int? count,
    String? description,
    String? attachmentUrl,
    RequestStatus? status,
    AdminResponse? adminResponse,
  }) {
    return Request(
      id: id ?? this.id,
      storeId: storeId ?? this.storeId,
      requestedAt: requestedAt ?? this.requestedAt,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      role: role ?? this.role,
      count: count ?? this.count,
      description: description ?? this.description,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
      status: status ?? this.status,
      adminResponse: adminResponse ?? this.adminResponse,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'storeId': storeId,
      'requestedAt': Timestamp.fromDate(requestedAt),
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'role': role,
      'count': count,
      'description': description,
      'attachmentUrl': attachmentUrl,
      'status': _statusToString(status),
      'adminResponse': adminResponse?.toMap(),
    };
  }

  factory Request.fromMap(Map<String, dynamic> map) {
    return Request(
      id: map['id'] ?? '',
      storeId: map['storeId'] ?? '',
      requestedAt: map['requestedAt'] is Timestamp 
          ? (map['requestedAt'] as Timestamp).toDate() 
          : DateTime.now(),
      startTime: map['startTime'] is Timestamp 
          ? (map['startTime'] as Timestamp).toDate() 
          : DateTime.now(),
      endTime: map['endTime'] is Timestamp 
          ? (map['endTime'] as Timestamp).toDate() 
          : DateTime.now().add(const Duration(hours: 1)),
      role: map['role'] ?? '',
      count: map['count'] ?? 1,
      description: map['description'] ?? '',
      attachmentUrl: map['attachmentUrl'],
      status: _stringToStatus(map['status']),
      adminResponse: map['adminResponse'] != null 
          ? AdminResponse.fromMap(map['adminResponse']) 
          : null,
    );
  }

  static RequestStatus _stringToStatus(String? status) {
    switch (status) {
      case 'pending':
        return RequestStatus.pending;
      case 'approved':
        return RequestStatus.approved;
      case 'denied':
        return RequestStatus.denied;
      default:
        return RequestStatus.pending;
    }
  }

  static String _statusToString(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return 'pending';
      case RequestStatus.approved:
        return 'approved';
      case RequestStatus.denied:
        return 'denied';
    }
  }
}

class AdminResponse {
  final DateTime respondedAt;
  final String message;

  AdminResponse({
    required this.respondedAt,
    required this.message,
  });

  Map<String, dynamic> toMap() {
    return {
      'respondedAt': Timestamp.fromDate(respondedAt),
      'message': message,
    };
  }

  factory AdminResponse.fromMap(Map<String, dynamic> map) {
    return AdminResponse(
      respondedAt: map['respondedAt'] is Timestamp 
          ? (map['respondedAt'] as Timestamp).toDate() 
          : DateTime.now(),
      message: map['message'] ?? '',
    );
  }
}
