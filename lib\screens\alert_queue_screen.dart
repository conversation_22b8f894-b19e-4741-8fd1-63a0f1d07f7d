import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/alarm.dart';
import '../services/auth_service.dart';
import '../services/audio_service.dart';
import '../services/alarm_service.dart';

class AlertQueueScreen extends StatefulWidget {
  final Alarm alarm;

  const AlertQueueScreen({
    super.key,
    required this.alarm,
  });

  @override
  State<AlertQueueScreen> createState() => _AlertQueueScreenState();
}

class _AlertQueueScreenState extends State<AlertQueueScreen> {
  final AudioService _audioService = AudioService();
  final AlarmService _alarmService = AlarmService();

  @override
  void initState() {
    super.initState();

    // Play sound and vibrate when screen appears
    _audioService.playAlarmSound(widget.alarm.type);
    HapticFeedback.heavyImpact();

    // Play sound again after a delay to ensure it gets attention
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        _audioService.playAlarmSound(widget.alarm.type);
        HapticFeedback.heavyImpact();
      }
    });
  }

  // Format timestamp for display
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      // Prevent back button from dismissing the screen
      canPop: false,
      child: Scaffold(
        backgroundColor: widget.alarm.type == AlarmType.red
            ? Colors.red.shade50
            : Colors.orange.shade50,
        appBar: AppBar(
          backgroundColor: widget.alarm.type == AlarmType.red
              ? Colors.red
              : Colors.orange,
          foregroundColor: Colors.white,
          title: Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                widget.alarm.type == AlarmType.red ? 'AKUT LARM' : 'VARNINGSLARM',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          automaticallyImplyLeading: false, // Remove back button
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 4,
                  color: Colors.white,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Larm ID: ${widget.alarm.id}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Enhet: ${widget.alarm.deviceId}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        Text(
                          'Plats: ${widget.alarm.location}',
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        Text(
                          'Tid: ${_formatDateTime(widget.alarm.timestamp)}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            const Text(
                              'Status: ',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                            _buildStatusBadge(widget.alarm.status),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'Åtgärd krävs',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.close),
                        label: const Text('Stäng'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.visibility),
                        label: const Text('Visa detaljer'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: widget.alarm.type == AlarmType.red
                              ? Colors.red
                              : Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        onPressed: () {
                          // Navigate to alarm detail screen
                          Navigator.of(context).pop(); // Close this screen first
                          AuthService.navigatorKey.currentState?.pushNamed(
                            '/alarm_detail',
                            arguments: widget.alarm.id
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(AlarmStatus status) {
    Color color;
    String text;

    switch (status) {
      case AlarmStatus.new_:
        color = Colors.red;
        text = 'Nytt';
        break;
      case AlarmStatus.ongoing:
        color = Colors.blue;
        text = 'Pågående';
        break;
      case AlarmStatus.resolved:
        color = Colors.green;
        text = 'Löst';
        break;
      default:
        color = Colors.grey;
        text = 'Okänd';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(color: color, fontWeight: FontWeight.bold),
      ),
    );
  }
}
