import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class representing an MQTT message from the mqtt_messages collection
class MqttMessage {
  final String id;
  final String clientid;
  final String payload;
  final String qos;
  final String topic;
  final String username;
  final DateTime receivedAt;

  /// Constructor for MqttMessage
  MqttMessage({
    required this.id,
    required this.clientid,
    required this.payload,
    required this.qos,
    required this.topic,
    required this.username,
    required this.receivedAt,
  });

  /// Create a copy of this MqttMessage with optional field updates
  MqttMessage copyWith({
    String? id,
    String? clientid,
    String? payload,
    String? qos,
    String? topic,
    String? username,
    DateTime? receivedAt,
  }) {
    return MqttMessage(
      id: id ?? this.id,
      clientid: clientid ?? this.clientid,
      payload: payload ?? this.payload,
      qos: qos ?? this.qos,
      topic: topic ?? this.topic,
      username: username ?? this.username,
      receivedAt: receivedAt ?? this.receivedAt,
    );
  }

  /// Convert MqttMessage to a Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'clientid': clientid,
      'payload': payload,
      'qos': qos,
      'topic': topic,
      'username': username,
      'receivedAt': Timestamp.fromDate(receivedAt),
    };
  }

  /// Create an MqttMessage from a Firestore document
  factory MqttMessage.fromMap(String id, Map<String, dynamic> map) {
    return MqttMessage(
      id: id,
      clientid: map['clientid'] ?? '',
      payload: map['payload'] ?? '',
      qos: map['qos']?.toString() ?? '0',
      topic: map['topic'] ?? '',
      username: map['username'] ?? '',
      receivedAt: map['receivedAt'] is Timestamp
          ? (map['receivedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  /// Get the alert type based on the payload
  AlertType get alertType {
    if (payload.toLowerCase().contains('rod larm')) {
      return AlertType.red;
    } else if (payload.toLowerCase().contains('gul larm')) {
      return AlertType.yellow;
    } else {
      return AlertType.unknown;
    }
  }

  /// Get a color based on the alert type
  String get alertTypeText {
    switch (alertType) {
      case AlertType.red:
        return 'RÖD LARM';
      case AlertType.yellow:
        return 'GUL LARM';
      case AlertType.unknown:
        return 'OKÄND';
    }
  }

  /// Extract store name from topic if available
  String? get storeName {
    final parts = topic.split('/');
    if (parts.length > 2 && parts[0] == '' && parts[1] == 'store') {
      return parts[2];
    }
    return null;
  }

  /// Format the received time as a string (HH:MM:SS)
  String get formattedTime {
    final hour = receivedAt.hour.toString().padLeft(2, '0');
    final minute = receivedAt.minute.toString().padLeft(2, '0');
    final second = receivedAt.second.toString().padLeft(2, '0');
    return '$hour:$minute:$second';
  }

  /// Format the received date as a string (YYYY-MM-DD)
  String get formattedDate {
    final year = receivedAt.year;
    final month = receivedAt.month.toString().padLeft(2, '0');
    final day = receivedAt.day.toString().padLeft(2, '0');
    return '$year-$month-$day';
  }
}

/// Enum representing different types of alerts
enum AlertType {
  red,
  yellow,
  unknown,
}
