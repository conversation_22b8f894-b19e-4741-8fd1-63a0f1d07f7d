import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/guard_order.dart';
import '../../services/store_service.dart';
import '../../services/file_service.dart';
import '../../utils/responsive_layout.dart';
import '../../widgets/attachment_list.dart';

class GuardOrderDetailScreen extends StatefulWidget {
  final String orderId;

  const GuardOrderDetailScreen({
    super.key,
    required this.orderId,
  });

  @override
  State<GuardOrderDetailScreen> createState() => _GuardOrderDetailScreenState();
}

class _GuardOrderDetailScreenState extends State<GuardOrderDetailScreen> {
  final _storeService = StoreService();

  late GuardOrder? _order;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrder();
  }

  void _loadOrder() {
    setState(() {
      _isLoading = true;
    });

    try {
      final order = _storeService.getGuardOrderById(widget.orderId);

      setState(() {
        _order = order;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _order = null;
        _isLoading = false;
      });
    }
  }

  Future<void> _cancelOrder() async {
    if (_order == null) return;

    // Visa bekräftelsedialog
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Avbryt beställning'),
        content: const Text('Är du säker på att du vill avbryta denna beställning?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Nej'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Ja, avbryt'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    // Avbryt beställningen
    final success = _storeService.cancelGuardOrder(_order!.id);

    if (success) {
      _loadOrder();

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Beställningen har avbrutits'),
          backgroundColor: Colors.green,
        ),
      );

      // Skicka tillbaka true för att indikera att beställningen har uppdaterats
      Navigator.pop(context, true);
    } else {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Kunde inte avbryta beställningen'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Beställningsdetaljer'),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_order == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Beställningsdetaljer'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Beställningen hittades inte',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Beställningen du söker efter finns inte eller har tagits bort.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back),
                label: const Text('Tillbaka'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Beställning - ${_order!.guardTypeText}'),
      ),
      body: SingleChildScrollView(
        padding: ResponsiveLayout.getAdaptivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statusbanner
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _order!.statusColor),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: _order!.statusColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Status: ${_order!.statusText}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _order!.statusColor,
                          ),
                        ),
                        if (_order!.updatedAt != null)
                          Text(
                            'Senast uppdaterad: ${DateFormat('yyyy-MM-dd HH:mm').format(_order!.updatedAt!)}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Beställningsdetaljer
            Text(
              'Beställningsdetaljer',
              style: TextStyle(
                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow('Beställnings-ID:', _order!.id),
                    _buildDetailRow('Skapad:', DateFormat('yyyy-MM-dd HH:mm').format(_order!.createdAt)),
                    _buildDetailRow('Butik:', _order!.storeName),
                    _buildDetailRow('Datum:', DateFormat('yyyy-MM-dd').format(_order!.requestedDate)),
                    _buildDetailRow('Tid:', '${_order!.startTime} - ${_order!.endTime}'),
                    _buildDetailRow('Typ av vakt:', _order!.guardTypeText),
                    _buildDetailRow('Antal:', '${_order!.numberOfGuards} ${_order!.numberOfGuards == 1 ? 'person' : 'personer'}'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Beskrivning
            Text(
              'Beskrivning',
              style: TextStyle(
                fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Text(_order!.description),
              ),
            ),

            // Bilagor
            if (_order!.attachmentPaths.isNotEmpty) ...[
              const SizedBox(height: 24),

              Text(
                'Bilagor',
                style: TextStyle(
                  fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: AttachmentList(
                    attachmentPaths: _order!.attachmentPaths,
                    readOnly: true,
                  ),
                ),
              ),
            ],

            // Tillstånd för ordningsvakt
            if (_order!.guardType == GuardType.securityGuard &&
                _order!.securityGuardPermitPath != null) ...[
              const SizedBox(height: 24),

              Text(
                'Tillstånd för ordningsvakt',
                style: TextStyle(
                  fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Bifogat tillstånd från Polismyndigheten:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      AttachmentList(
                        attachmentPaths: [_order!.securityGuardPermitPath!],
                        readOnly: true,
                      ),
                    ],
                  ),
                ),
              ),
            ],

            // Visa feedback om det finns
            if (_order!.adminFeedback != null && _order!.adminFeedback!.isNotEmpty) ...[
              const SizedBox(height: 24),

              Text(
                'Feedback från admin',
                style: TextStyle(
                  fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.comment, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Meddelande:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(_order!.adminFeedback!),
                    ],
                  ),
                ),
              ),
            ],

            // Visa statushistorik om det finns
            if (_order!.statusHistory.isNotEmpty) ...[
              const SizedBox(height: 24),

              Text(
                'Statushistorik',
                style: TextStyle(
                  fontSize: ResponsiveLayout.getAdaptiveFontSize(context, 18),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      for (final change in _order!.statusHistory) ...[
                        Builder(builder: (context) {
                          final dateStr = DateFormat('yyyy-MM-dd HH:mm').format(change.timestamp);
                          Color statusColor;
                          switch (change.toStatus) {
                            case OrderStatus.pending:
                              statusColor = Colors.orange;
                              break;
                            case OrderStatus.approved:
                              statusColor = Colors.green;
                              break;
                            case OrderStatus.rejected:
                              statusColor = Colors.red;
                              break;
                            case OrderStatus.completed:
                              statusColor = Colors.blue;
                              break;
                            case OrderStatus.cancelled:
                              statusColor = Colors.red;
                              break;
                          }

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.circle, size: 12, color: statusColor),
                                  const SizedBox(width: 8),
                                  Text(
                                    dateStr,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                  if (change.userName != null) ...[
                                    const SizedBox(width: 8),
                                    Text(
                                      'av ${change.userName}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey.shade700,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Text(
                                  change.note ?? 'Status ändrad från ${_getStatusText(change.fromStatus)} till ${_getStatusText(change.toStatus)}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade800,
                                  ),
                                ),
                              ),
                              const Divider(),
                            ],
                          );
                        }),
                      ],
                    ],
                  ),
                ),
              ),
            ],

            const SizedBox(height: 32),

            // Knappar
            if (_order!.status == OrderStatus.pending || _order!.status == OrderStatus.approved)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _cancelOrder,
                  icon: const Icon(Icons.cancel),
                  label: const Text('AVBRYT BESTÄLLNING'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Väntande';
      case OrderStatus.approved:
        return 'Godkänd';
      case OrderStatus.rejected:
        return 'Avvisad';
      case OrderStatus.completed:
        return 'Genomförd';
      case OrderStatus.cancelled:
        return 'Avbruten';
    }
  }
}
