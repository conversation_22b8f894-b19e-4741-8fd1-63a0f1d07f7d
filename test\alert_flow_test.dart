import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import '../lib/models/alert.dart';
import '../lib/models/button.dart';
import '../lib/models/store.dart';
import '../lib/models/user.dart';
import '../lib/services/database_service.dart';
import '../lib/services/alert_service.dart';
import '../lib/services/notification_service.dart';

@GenerateMocks([
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
  QuerySnapshot,
  DatabaseService,
  AlertService,
  NotificationService,
])
void main() {
  group('Alert Flow Tests', () {
    late MockDatabaseService mockDatabaseService;
    late MockAlertService mockAlertService;
    late MockNotificationService mockNotificationService;
    
    setUp(() {
      mockDatabaseService = MockDatabaseService();
      mockAlertService = MockAlertService();
      mockNotificationService = MockNotificationService();
    });
    
    test('MQTT message creates alert', () async {
      // Create test data
      final mqttMessage = {
        'clientid': 'button-1',
        'topic': 'button/press',
        'payload': 'pressed',
        'qos': 1,
        'receivedAt': Timestamp.now(),
        'username': 'mqtt-user',
      };
      
      final button = Button(
        id: 'button-1',
        label: 'Test Button',
        storeId: 'store-1',
        status: ButtonStatus.active,
      );
      
      final store = Store(
        id: 'store-1',
        name: 'Test Store',
        address: 'Test Address',
        city: 'Test City',
        postalCode: '12345',
      );
      
      final alert = Alert(
        id: 'alert-1',
        buttonId: 'button-1',
        storeId: 'store-1',
        pressedAt: DateTime.now(),
        status: AlertStatus.active,
      );
      
      // Mock database service behavior
      when(mockDatabaseService.buttonsCollection.doc('button-1').get())
          .thenAnswer((_) async => MockDocumentSnapshot());
      when(mockDatabaseService.alertsCollection.add(any))
          .thenAnswer((_) async => MockDocumentReference());
      
      // Mock alert service behavior
      when(mockAlertService.createAlert('button-1', 'store-1'))
          .thenAnswer((_) async => alert);
      
      // Test that MQTT message creates alert
      final createdAlert = await mockAlertService.createAlert('button-1', 'store-1');
      expect(createdAlert, isNotNull);
      expect(createdAlert?.buttonId, 'button-1');
      expect(createdAlert?.storeId, 'store-1');
      expect(createdAlert?.status, AlertStatus.active);
    });
    
    test('Alert notifies appropriate users', () async {
      // Create test data
      final alert = Alert(
        id: 'alert-1',
        buttonId: 'button-1',
        storeId: 'store-1',
        pressedAt: DateTime.now(),
        status: AlertStatus.active,
      );
      
      final store = Store(
        id: 'store-1',
        name: 'Test Store',
        address: 'Test Address',
        city: 'Test City',
        postalCode: '12345',
      );
      
      final adminUser = User(
        id: 'admin-id',
        username: 'admin',
        name: 'Admin User',
        role: UserRole.admin,
        email: '<EMAIL>',
      );
      
      final guardUser = User(
        id: 'guard-id',
        username: 'guard',
        name: 'Guard User',
        role: UserRole.guard,
        email: '<EMAIL>',
        stores: ['store-1'],
      );
      
      final storeUser = User(
        id: 'store-id',
        username: 'store',
        name: 'Store User',
        role: UserRole.customer,
        email: '<EMAIL>',
        storeId: 'store-1',
      );
      
      // Mock notification service behavior
      when(mockNotificationService.createNotification(
        userId: adminUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: any,
        message: any,
      )).thenAnswer((_) async => null);
      
      when(mockNotificationService.createNotification(
        userId: guardUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: any,
        message: any,
      )).thenAnswer((_) async => null);
      
      when(mockNotificationService.createNotification(
        userId: storeUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: any,
        message: any,
      )).thenAnswer((_) async => null);
      
      // Test that alert notifies appropriate users
      await mockNotificationService.createNotification(
        userId: adminUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: 'New Alert',
        message: 'Alert from ${store.name}',
      );
      
      await mockNotificationService.createNotification(
        userId: guardUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: 'New Alert',
        message: 'Alert from ${store.name}',
      );
      
      await mockNotificationService.createNotification(
        userId: storeUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: 'New Alert',
        message: 'Alert from ${store.name}',
      );
      
      // Verify that notifications were created
      verify(mockNotificationService.createNotification(
        userId: adminUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: any,
        message: any,
      )).called(1);
      
      verify(mockNotificationService.createNotification(
        userId: guardUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: any,
        message: any,
      )).called(1);
      
      verify(mockNotificationService.createNotification(
        userId: storeUser.id,
        alertId: alert.id,
        storeId: store.id,
        storeName: store.name,
        title: any,
        message: any,
      )).called(1);
    });
  });
}

class MockDocumentSnapshot extends Mock implements DocumentSnapshot {
  @override
  bool get exists => true;
  
  @override
  Map<String, dynamic> data() => {
    'id': 'button-1',
    'label': 'Test Button',
    'storeId': 'store-1',
    'status': 'active',
  };
}

class MockDocumentReference extends Mock implements DocumentReference {
  @override
  String get id => 'alert-1';
}
