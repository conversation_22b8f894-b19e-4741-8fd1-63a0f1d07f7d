const { onDocumentWritten } = require("firebase-functions/v2/firestore");
const admin = require("firebase-admin");

// This function sets custom claims on a user's token based on their role in Firestore
exports.setUserClaims = onDocumentWritten({
  document: 'users/{userId}',
  region: 'europe-west3'
}, async (event) => {
  const change = event.data;
  const userId = event.params.userId;

  // If the document was deleted, remove all custom claims
  if (!change.after.exists) {
    try {
      await admin.auth().setCustomUserClaims(userId, null);
      console.log(`Removed all custom claims for user ${userId}`);
      return null;
    } catch (error) {
      console.error(`Error removing custom claims for user ${userId}:`, error);
      return null;
    }
  }

  // Get the user data after the change
  const userData = change.after.data();

  // If no role is defined, exit
  if (userData.role === undefined) {
    console.log(`No role defined for user ${userId}`);
    return null;
  }

  // Map role index to role name
  const roleMap = {
    0: 'guard',
    1: 'admin',
    2: 'store'
  };

  const roleName = roleMap[userData.role];

  if (!roleName) {
    console.error(`Invalid role index ${userData.role} for user ${userId}`);
    return null;
  }

  // Prepare custom claims
  const claims = {
    role: roleName
  };

  // Store assignments are stored in Firestore, not in custom claims
  // This prevents issues with custom claims size limitations when guards have many stores assigned
  // Only store users get their single store in claims for backward compatibility
  if (roleName === 'store' && userData.storeId) {
    claims.stores = [userData.storeId];
  }

  try {
    // Set custom claims
    await admin.auth().setCustomUserClaims(userId, claims);
    console.log(`Set custom claims for user ${userId}:`, claims);

    // We no longer update the customClaimsUpdated field to avoid unnecessary writes
    // and potential infinite loops

    return null;
  } catch (error) {
    console.error(`Error setting custom claims for user ${userId}:`, error);
    return null;
  }
  });
