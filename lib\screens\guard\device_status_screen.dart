import 'package:flutter/material.dart';
import '../../models/iot_device.dart';
import '../../models/store.dart';
import '../../services/store_service.dart';
import '../../widgets/home_button.dart';

class DeviceStatusScreen extends StatefulWidget {
  const DeviceStatusScreen({super.key});

  @override
  State<DeviceStatusScreen> createState() => _DeviceStatusScreenState();
}

class _DeviceStatusScreenState extends State<DeviceStatusScreen> {
  final StoreService _storeService = StoreService();
  final TextEditingController _searchController = TextEditingController();

  List<IoTDevice> _devices = [];
  List<IoTDevice> _filteredDevices = [];
  Map<String, Store> _storeMap = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadData() {
    _devices = _storeService.getAllDevices();
    _filteredDevices = List.from(_devices);

    // Skapa en map av butiker för snabb uppslag
    final stores = _storeService.getAllStores();
    _storeMap = {for (var store in stores) store.id: store};

    setState(() {});
  }

  void _filterDevices(String query) {
    if (query.isEmpty) {
      _filteredDevices = List.from(_devices);
    } else {
      _filteredDevices = _storeService.searchDevices(query);
    }
    setState(() {});
  }

  String _getStoreName(String storeId) {
    return _storeMap[storeId]?.name ?? 'Okänd butik';
  }

  Color _getBatteryColor(int level) {
    if (level <= 10) return Colors.red;
    if (level <= 20) return Colors.orange;
    if (level <= 50) return Colors.yellow;
    return Colors.green;
  }

  @override
  Widget build(BuildContext context) {
    // Räkna enheter med lågt batteri
    final lowBatteryDevices = _devices.where((d) => d.isLowBattery).length;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhetsstatus'),
        actions: const [
          HomeButton(),
        ],
      ),
      body: Column(
        children: [
          // Sökfält
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Sök enheter',
                hintText: 'Ange namn, ID eller butik...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterDevices('');
                        },
                      )
                    : null,
              ),
              onChanged: _filterDevices,
            ),
          ),

          // Varning för lågt batteri
          if (lowBatteryDevices > 0)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.battery_alert, color: Colors.red.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '$lowBatteryDevices ${lowBatteryDevices == 1 ? 'enhet har' : 'enheter har'} lågt batteri!',
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Enhetslista
          Expanded(
            child: _filteredDevices.isEmpty
                ? const Center(
                    child: Text(
                      'Inga enheter hittades',
                      style: TextStyle(fontSize: 16),
                    ),
                  )
                : ListView.builder(
                    itemCount: _filteredDevices.length,
                    itemBuilder: (context, index) {
                      final device = _filteredDevices[index];
                      final storeName = _getStoreName(device.storeId);

                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.device_hub,
                                    color: device.isActive
                                        ? Colors.green
                                        : Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      device.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: device.status == DeviceStatus.active
                                          ? Colors.green
                                          : device.status == DeviceStatus.maintenance
                                              ? Colors.orange
                                              : Colors.red,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      device.status == DeviceStatus.active
                                          ? 'Aktiv'
                                          : device.status == DeviceStatus.maintenance
                                              ? 'Underhåll'
                                              : 'Inaktiv',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text('Butik: $storeName'),
                              Text('ID: ${device.id}'),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  const Text('Batteri:'),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: LinearProgressIndicator(
                                      value: device.batteryLevel / 100,
                                      backgroundColor: Colors.grey.shade200,
                                      color: _getBatteryColor(device.batteryLevel),
                                      minHeight: 10,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getBatteryColor(device.batteryLevel),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      '${device.batteryLevel}%',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Senast aktiv: ${_formatDateTime(device.lastActive)}',
                                style: TextStyle(
                                  color: DateTime.now().difference(device.lastActive).inDays > 7
                                      ? Colors.red
                                      : null,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
